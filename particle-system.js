// Advanced Particle System
class ParticleSystem {
    constructor() {
        this.container = null;
        this.particles = [];
        this.isRunning = false;
        this.settings = {
            maxParticles: 50,
            spawnRate: 2,
            types: ['type-1', 'type-2', 'type-3', 'glow', 'pulse', 'float'],
            specialTypes: ['binary', 'code', 'spiral', 'zigzag'],
            density: 'medium',
            interactive: true,
            performance: 'auto'
        };
        
        this.binaryChars = ['0', '1'];
        this.codeSnippets = [
            'if', 'else', 'for', 'while', 'function', 'var', 'let', 'const',
            '{}', '[]', '()', '=>', '&&', '||', '==', '!=', '++', '--',
            'CGG', 'hack', 'code', 'secure', 'encrypt', 'decode'
        ];
        
        this.init();
    }
    
    init() {
        this.createContainer();
        this.detectPerformance();
        this.loadSettings();
        this.setupEventListeners();
        this.start();
    }
    
    createContainer() {
        if (document.querySelector('.particle-container')) {
            this.container = document.querySelector('.particle-container');
            return;
        }
        
        this.container = document.createElement('div');
        this.container.className = 'particle-container';
        
        const homeTerminal = document.querySelector('.home-terminal');
        if (homeTerminal) {
            homeTerminal.appendChild(this.container);
        } else {
            document.body.appendChild(this.container);
        }
    }
    
    detectPerformance() {
        if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
            this.settings.performance = 'low';
            this.settings.maxParticles = 15;
            return;
        }
        
        if (navigator.deviceMemory && navigator.deviceMemory < 4) {
            this.settings.performance = 'low';
            this.settings.maxParticles = 25;
        } else {
            this.settings.performance = 'high';
            this.settings.maxParticles = 60;
        }
    }
    
    setupEventListeners() {
        document.addEventListener('themeChanged', (e) => {
            this.updateColors(e.detail.colors);
        });
        
        if (this.settings.interactive) {
            document.addEventListener('click', (e) => {
                if (e.target.closest('.particle-container')) {
                    this.createBurst(e.clientX, e.clientY);
                }
            });
        }
        
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pause();
            } else {
                this.resume();
            }
        });
    }
    
    start() {
        if (this.isRunning) return;
        this.isRunning = true;
        this.spawnLoop();
        this.cleanupLoop();
    }
    
    pause() {
        this.isRunning = false;
    }
    
    resume() {
        if (!this.isRunning) {
            this.start();
        }
    }
    
    spawnLoop() {
        if (!this.isRunning) return;
        
        for (let i = 0; i < this.settings.spawnRate; i++) {
            if (this.particles.length < this.settings.maxParticles) {
                this.createParticle();
            }
        }
        
        const delay = this.settings.performance === 'low' ? 1000 : 500;
        setTimeout(() => this.spawnLoop(), delay);
    }
    
    createParticle() {
        const particle = document.createElement('div');
        particle.className = 'particle';
        
        // Random type
        const allTypes = [...this.settings.types, ...this.settings.specialTypes];
        const type = allTypes[Math.floor(Math.random() * allTypes.length)];
        particle.classList.add(type);
        
        // Position
        particle.style.left = Math.random() * 100 + '%';
        
        // Special content for binary and code particles
        if (type === 'binary') {
            particle.setAttribute('data-binary', this.binaryChars[Math.floor(Math.random() * this.binaryChars.length)]);
        } else if (type === 'code') {
            particle.setAttribute('data-code', this.codeSnippets[Math.floor(Math.random() * this.codeSnippets.length)]);
        }
        
        // Interactive particles
        if (this.settings.interactive && Math.random() < 0.1) {
            particle.classList.add('interactive');
            particle.addEventListener('click', (e) => {
                e.stopPropagation();
                this.createBurst(e.clientX, e.clientY);
                particle.remove();
            });
        }
        
        this.container.appendChild(particle);
        this.particles.push({ element: particle, type: type });
    }
    
    createBurst(x, y) {
        const burst = document.createElement('div');
        burst.className = 'particle-burst';
        burst.style.left = x + 'px';
        burst.style.top = y + 'px';
        
        // Create burst particles
        for (let i = 0; i < 8; i++) {
            const burstParticle = document.createElement('div');
            burstParticle.className = 'burst-particle';
            
            const angle = (i / 8) * Math.PI * 2;
            const distance = 50 + Math.random() * 50;
            const burstX = Math.cos(angle) * distance;
            const burstY = Math.sin(angle) * distance;
            
            burstParticle.style.setProperty('--burst-x', burstX + 'px');
            burstParticle.style.setProperty('--burst-y', burstY + 'px');
            
            burst.appendChild(burstParticle);
        }
        
        document.body.appendChild(burst);
        
        setTimeout(() => {
            burst.remove();
        }, 1000);
    }
    
    cleanupLoop() {
        if (!this.isRunning) return;
        
        this.particles = this.particles.filter(particle => {
            if (particle.element && particle.element.parentNode) {
                const rect = particle.element.getBoundingClientRect();
                if (rect.bottom < -50 || rect.top > window.innerHeight + 50) {
                    particle.element.remove();
                    return false;
                }
                return true;
            }
            return false;
        });
        
        setTimeout(() => this.cleanupLoop(), 2000);
    }
    
    updateColors(colors) {
        const root = document.documentElement;
        root.style.setProperty('--particle-color', colors.text);
        
        // Update existing particles
        this.particles.forEach(particle => {
            if (particle.element) {
                particle.element.style.color = colors.text;
                if (particle.element.classList.contains('glow')) {
                    particle.element.style.boxShadow = `0 0 6px ${colors.text}, 0 0 12px ${colors.text}`;
                }
            }
        });
    }
    
    clearAllParticles() {
        this.particles.forEach(particle => {
            if (particle.element && particle.element.parentNode) {
                particle.element.remove();
            }
        });
        this.particles = [];
    }
    
    setDensity(density) {
        this.settings.density = density;
        switch(density) {
            case 'low':
                this.settings.maxParticles = Math.floor(this.settings.maxParticles * 0.5);
                this.settings.spawnRate = 1;
                break;
            case 'high':
                this.settings.maxParticles = Math.floor(this.settings.maxParticles * 1.5);
                this.settings.spawnRate = 3;
                break;
            default:
                // medium - keep defaults
                break;
        }
        this.saveSettings();
    }
    
    saveSettings() {
        localStorage.setItem('particleSystemSettings', JSON.stringify(this.settings));
    }
}

// Initialize particle system
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize on home page
    if (document.querySelector('.home-terminal')) {
        window.particleSystem = new ParticleSystem();
    }
});
