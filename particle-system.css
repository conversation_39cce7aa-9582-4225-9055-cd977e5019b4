/* Particle System Styles */
.particle-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--particle-color, #00ff41);
    border-radius: 50%;
    opacity: 0.7;
    animation: particleFloat linear infinite;
}

/* Different particle types */
.particle.type-1 {
    width: 1px;
    height: 1px;
    animation-duration: 8s;
}

.particle.type-2 {
    width: 2px;
    height: 2px;
    animation-duration: 12s;
    box-shadow: 0 0 4px var(--particle-color, #00ff41);
}

.particle.type-3 {
    width: 3px;
    height: 3px;
    animation-duration: 15s;
    box-shadow: 0 0 6px var(--particle-color, #00ff41);
}

.particle.type-4 {
    width: 1px;
    height: 8px;
    border-radius: 1px;
    animation-duration: 10s;
    opacity: 0.5;
}

/* Glowing particles */
.particle.glow {
    box-shadow: 
        0 0 6px var(--particle-color, #00ff41),
        0 0 12px var(--particle-color, #00ff41),
        0 0 18px var(--particle-color, #00ff41);
    animation-duration: 6s;
}

/* Pulsing particles */
.particle.pulse {
    animation: particleFloat linear infinite, particlePulse 2s ease-in-out infinite;
}

/* Floating particles */
.particle.float {
    animation: particleFloat linear infinite, particleFloatSide 4s ease-in-out infinite;
}

/* Particle animations */
@keyframes particleFloat {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 0.7;
    }
    90% {
        opacity: 0.7;
    }
    100% {
        transform: translateY(-10vh) translateX(0);
        opacity: 0;
    }
}

@keyframes particlePulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.5);
        opacity: 1;
    }
}

@keyframes particleFloatSide {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(10px);
    }
    75% {
        transform: translateX(-10px);
    }
}

/* Spiral particles */
.particle.spiral {
    animation: particleSpiral 20s linear infinite;
}

@keyframes particleSpiral {
    0% {
        transform: translateY(100vh) rotate(0deg) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 0.7;
    }
    90% {
        opacity: 0.7;
    }
    100% {
        transform: translateY(-10vh) rotate(720deg) translateX(50px);
        opacity: 0;
    }
}

/* Zigzag particles */
.particle.zigzag {
    animation: particleZigzag 15s linear infinite;
}

@keyframes particleZigzag {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 0.7;
    }
    25% {
        transform: translateY(75vh) translateX(20px);
    }
    50% {
        transform: translateY(50vh) translateX(-20px);
    }
    75% {
        transform: translateY(25vh) translateX(20px);
    }
    90% {
        opacity: 0.7;
    }
    100% {
        transform: translateY(-10vh) translateX(-20px);
        opacity: 0;
    }
}

/* Binary particles (0 and 1) */
.particle.binary {
    width: auto;
    height: auto;
    background: none;
    color: var(--particle-color, #00ff41);
    font-family: 'Fira Code', monospace;
    font-size: 12px;
    font-weight: bold;
    text-shadow: 0 0 8px var(--particle-color, #00ff41);
}

.particle.binary::before {
    content: attr(data-binary);
}

/* Code particles */
.particle.code {
    width: auto;
    height: auto;
    background: none;
    color: var(--particle-color, #00ff41);
    font-family: 'Fira Code', monospace;
    font-size: 10px;
    opacity: 0.6;
    text-shadow: 0 0 4px var(--particle-color, #00ff41);
}

.particle.code::before {
    content: attr(data-code);
}

/* Interactive particles */
.particle.interactive {
    cursor: pointer;
    pointer-events: auto;
    transition: all 0.3s ease;
}

.particle.interactive:hover {
    transform: scale(2);
    opacity: 1;
    box-shadow: 
        0 0 10px var(--particle-color, #00ff41),
        0 0 20px var(--particle-color, #00ff41),
        0 0 30px var(--particle-color, #00ff41);
}

/* Particle trails */
.particle.trail::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 200%;
    background: linear-gradient(
        to bottom,
        transparent 0%,
        var(--particle-color, #00ff41) 50%,
        transparent 100%
    );
    opacity: 0.3;
    transform: translateY(-50%);
}

/* Particle burst effect */
.particle-burst {
    position: absolute;
    pointer-events: none;
}

.particle-burst .burst-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--particle-color, #00ff41);
    border-radius: 50%;
    animation: burstParticle 1s ease-out forwards;
}

@keyframes burstParticle {
    0% {
        transform: scale(1) translate(0, 0);
        opacity: 1;
    }
    100% {
        transform: scale(0) translate(var(--burst-x, 0), var(--burst-y, 0));
        opacity: 0;
    }
}

/* Performance optimizations */
.particle-container {
    will-change: transform;
    transform: translateZ(0);
}

.particle {
    will-change: transform, opacity;
    transform: translateZ(0);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .particle {
        animation-duration: 20s;
        opacity: 0.3;
    }
    
    .particle.pulse,
    .particle.float {
        animation: particleFloat linear infinite;
    }
    
    .particle.spiral,
    .particle.zigzag {
        animation: particleFloat linear infinite;
    }
}

/* Theme-specific particle adjustments */
.theme-cyberpunk .particle {
    box-shadow: 0 0 8px var(--particle-color);
}

.theme-retro .particle {
    border-radius: 0;
    transform: rotate(45deg);
}

.theme-hacker .particle.glow {
    box-shadow: 
        0 0 10px var(--particle-color),
        0 0 20px var(--particle-color),
        0 0 30px var(--particle-color);
}

/* Particle density controls */
.particle-density-low .particle:nth-child(2n) {
    display: none;
}

.particle-density-high .particle {
    animation-duration: calc(var(--duration) * 0.7);
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .particle-container {
        display: none; /* Disable on mobile for performance */
    }
}

@media (max-width: 1024px) {
    .particle.type-3,
    .particle.type-4 {
        display: none; /* Reduce particle count on tablets */
    }
}
