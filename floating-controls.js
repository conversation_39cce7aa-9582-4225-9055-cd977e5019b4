// Floating Controls Manager
class FloatingControls {
    constructor() {
        this.controls = document.getElementById('floating-controls');
        this.isDragging = false;
        this.isMinimized = false;
        this.isHidden = false;
        this.dragOffset = { x: 0, y: 0 };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadPosition();
        this.setupDragging();
        this.loadSoundState();
    }

    setupEventListeners() {
        // Replay button
        document.getElementById('replay-btn').addEventListener('click', () => {
            this.handleReplay();
        });

        // Sound toggle
        document.getElementById('sound-toggle').addEventListener('click', () => {
            this.handleSoundToggle();
        });

        // Fullscreen toggle
        document.getElementById('fullscreen-btn').addEventListener('click', () => {
            this.handleFullscreen();
        });

        // Close controls
        document.getElementById('close-controls').addEventListener('click', () => {
            this.hideControls();
        });

        // Double click to minimize/restore
        this.controls.addEventListener('dblclick', () => {
            this.toggleMinimize();
        });

        // Show controls on page interaction
        document.addEventListener('mousemove', () => {
            if (this.isHidden) {
                this.showControls();
            }
        });

        // Auto-hide after inactivity
        this.setupAutoHide();
    }

    setupDragging() {
        const dragHandle = this.controls.querySelector('.drag-handle');

        // Mouse events
        dragHandle.addEventListener('mousedown', (e) => {
            this.startDrag(e);
        });

        this.controls.addEventListener('mousedown', (e) => {
            if (e.target === this.controls || e.target.classList.contains('drag-handle')) {
                this.startDrag(e);
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (this.isDragging) {
                this.drag(e);
            }
        });

        document.addEventListener('mouseup', () => {
            this.stopDrag();
        });

        // Touch events for mobile
        dragHandle.addEventListener('touchstart', (e) => {
            this.startDrag(e.touches[0]);
        });

        document.addEventListener('touchmove', (e) => {
            if (this.isDragging) {
                e.preventDefault();
                this.drag(e.touches[0]);
            }
        });

        document.addEventListener('touchend', () => {
            this.stopDrag();
        });
    }

    startDrag(e) {
        this.isDragging = true;
        this.controls.classList.add('dragging');

        const rect = this.controls.getBoundingClientRect();
        this.dragOffset.x = e.clientX - rect.left;
        this.dragOffset.y = e.clientY - rect.top;

        document.body.style.userSelect = 'none';
    }

    drag(e) {
        if (!this.isDragging) return;

        const x = e.clientX - this.dragOffset.x;
        const y = e.clientY - this.dragOffset.y;

        // Keep within viewport bounds
        const maxX = window.innerWidth - this.controls.offsetWidth;
        const maxY = window.innerHeight - this.controls.offsetHeight;

        const boundedX = Math.max(0, Math.min(x, maxX));
        const boundedY = Math.max(0, Math.min(y, maxY));

        this.controls.style.left = boundedX + 'px';
        this.controls.style.top = boundedY + 'px';
        this.controls.style.right = 'auto';
        this.controls.style.bottom = 'auto';
    }

    stopDrag() {
        if (!this.isDragging) return;

        this.isDragging = false;
        this.controls.classList.remove('dragging');
        document.body.style.userSelect = '';

        this.savePosition();
    }

    handleReplay() {
        const btn = document.getElementById('replay-btn');
        btn.classList.add('pulsing');

        // Check current page and trigger appropriate replay
        const currentPage = document.querySelector('.page-content.active');
        if (currentPage && currentPage.id === 'page-home') {
            // Home page - restart terminal animation
            if (window.homeTerminal && window.homeTerminal.startTerminalSequence) {
                window.homeTerminal.startTerminalSequence();
            }
        } else {
            // Other pages - refresh content or reload page
            window.location.reload();
        }

        setTimeout(() => {
            btn.classList.remove('pulsing');
        }, 2000);
    }

    handleSoundToggle() {
        const btn = document.getElementById('sound-toggle');
        const isActive = btn.classList.contains('active');

        if (isActive) {
            btn.classList.remove('active');
            btn.classList.add('muted');
            // Disable sound globally
            localStorage.setItem('globalSoundEnabled', 'false');
            if (window.homeTerminal) {
                window.homeTerminal.settings.soundEnabled = false;
            }
            // Mute all audio elements
            document.querySelectorAll('audio').forEach(audio => {
                audio.muted = true;
            });
        } else {
            btn.classList.add('active');
            btn.classList.remove('muted');
            // Enable sound globally
            localStorage.setItem('globalSoundEnabled', 'true');
            if (window.homeTerminal) {
                window.homeTerminal.settings.soundEnabled = true;
            }
            // Unmute all audio elements
            document.querySelectorAll('audio').forEach(audio => {
                audio.muted = false;
            });
        }
    }

    handleFullscreen() {
        const btn = document.getElementById('fullscreen-btn');

        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().then(() => {
                btn.classList.add('active');
            }).catch(err => {
                console.log('Fullscreen error:', err);
            });
        } else {
            document.exitFullscreen().then(() => {
                btn.classList.remove('active');
            });
        }
    }

    toggleMinimize() {
        this.isMinimized = !this.isMinimized;

        if (this.isMinimized) {
            this.controls.classList.add('minimized');
        } else {
            this.controls.classList.remove('minimized');
        }

        this.savePosition();
    }

    hideControls() {
        this.isHidden = true;
        this.controls.classList.add('hidden');
    }

    showControls() {
        this.isHidden = false;
        this.controls.classList.remove('hidden');
    }

    setupAutoHide() {
        let hideTimer;

        const resetTimer = () => {
            clearTimeout(hideTimer);
            hideTimer = setTimeout(() => {
                if (!this.isDragging && !this.controls.matches(':hover')) {
                    this.controls.style.opacity = '0.5';
                }
            }, 5000);
        };

        document.addEventListener('mousemove', resetTimer);
        document.addEventListener('keypress', resetTimer);

        this.controls.addEventListener('mouseenter', () => {
            this.controls.style.opacity = '0.9';
            clearTimeout(hideTimer);
        });

        this.controls.addEventListener('mouseleave', resetTimer);
    }

    savePosition() {
        const rect = this.controls.getBoundingClientRect();
        const position = {
            x: rect.left,
            y: rect.top,
            isMinimized: this.isMinimized
        };

        localStorage.setItem('floatingControlsPosition', JSON.stringify(position));
    }

    loadPosition() {
        const saved = localStorage.getItem('floatingControlsPosition');
        if (saved) {
            try {
                const position = JSON.parse(saved);

                // Ensure position is within viewport
                const maxX = window.innerWidth - this.controls.offsetWidth;
                const maxY = window.innerHeight - this.controls.offsetHeight;

                const x = Math.max(0, Math.min(position.x, maxX));
                const y = Math.max(0, Math.min(position.y, maxY));

                this.controls.style.left = x + 'px';
                this.controls.style.top = y + 'px';
                this.controls.style.right = 'auto';
                this.controls.style.bottom = 'auto';

                if (position.isMinimized) {
                    this.isMinimized = true;
                    this.controls.classList.add('minimized');
                }
            } catch (e) {
                console.log('Error loading position:', e);
            }
        }
    }

    loadSoundState() {
        const soundEnabled = localStorage.getItem('globalSoundEnabled');
        const btn = document.getElementById('sound-toggle');

        if (soundEnabled === 'false') {
            btn.classList.remove('active');
            btn.classList.add('muted');
        } else {
            btn.classList.add('active');
            btn.classList.remove('muted');
        }
    }
}

// Initialize floating controls when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.floatingControls = new FloatingControls();
});

// Handle fullscreen change events
document.addEventListener('fullscreenchange', () => {
    const btn = document.getElementById('fullscreen-btn');
    if (document.fullscreenElement) {
        btn.classList.add('active');
    } else {
        btn.classList.remove('active');
    }
});
