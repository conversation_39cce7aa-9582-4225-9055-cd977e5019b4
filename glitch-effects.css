/* Advanced Glitch Effects System */

/* Base glitch effect */
.glitch {
    position: relative;
    display: inline-block;
}

.glitch::before,
.glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
}

/* Red glitch layer */
.glitch::before {
    animation: glitch-1 0.5s infinite linear alternate-reverse;
    color: #ff0000;
    z-index: -1;
}

/* Blue glitch layer */
.glitch::after {
    animation: glitch-2 0.5s infinite linear alternate-reverse;
    color: #00ffff;
    z-index: -2;
}

/* Glitch animations */
@keyframes glitch-1 {
    0% {
        transform: translateX(0);
        clip-path: polygon(0 0%, 100% 0%, 100% 5%, 0 5%);
    }
    5% {
        transform: translateX(-2px);
        clip-path: polygon(0 0%, 100% 0%, 100% 10%, 0 10%);
    }
    10% {
        transform: translateX(-1px);
        clip-path: polygon(0 10%, 100% 10%, 100% 20%, 0 20%);
    }
    15% {
        transform: translateX(1px);
        clip-path: polygon(0 20%, 100% 20%, 100% 30%, 0 30%);
    }
    20% {
        transform: translateX(-1px);
        clip-path: polygon(0 30%, 100% 30%, 100% 40%, 0 40%);
    }
    25% {
        transform: translateX(2px);
        clip-path: polygon(0 40%, 100% 40%, 100% 50%, 0 50%);
    }
    30% {
        transform: translateX(-2px);
        clip-path: polygon(0 50%, 100% 50%, 100% 60%, 0 60%);
    }
    35% {
        transform: translateX(1px);
        clip-path: polygon(0 60%, 100% 60%, 100% 70%, 0 70%);
    }
    40% {
        transform: translateX(-1px);
        clip-path: polygon(0 70%, 100% 70%, 100% 80%, 0 80%);
    }
    45% {
        transform: translateX(2px);
        clip-path: polygon(0 80%, 100% 80%, 100% 90%, 0 90%);
    }
    50% {
        transform: translateX(-2px);
        clip-path: polygon(0 90%, 100% 90%, 100% 100%, 0 100%);
    }
    55% {
        transform: translateX(1px);
        clip-path: polygon(0 0%, 100% 0%, 100% 100%, 0 100%);
    }
    100% {
        transform: translateX(0);
        clip-path: polygon(0 0%, 100% 0%, 100% 100%, 0 100%);
    }
}

@keyframes glitch-2 {
    0% {
        transform: translateX(0);
        clip-path: polygon(0 15%, 100% 15%, 100% 30%, 0 30%);
    }
    15% {
        transform: translateX(2px);
        clip-path: polygon(0 3%, 100% 3%, 100% 9%, 0 9%);
    }
    25% {
        transform: translateX(-1px);
        clip-path: polygon(0 8%, 100% 8%, 100% 20%, 0 20%);
    }
    30% {
        transform: translateX(1px);
        clip-path: polygon(0 20%, 100% 20%, 100% 25%, 0 25%);
    }
    45% {
        transform: translateX(-2px);
        clip-path: polygon(0 40%, 100% 40%, 100% 60%, 0 60%);
    }
    60% {
        transform: translateX(2px);
        clip-path: polygon(0 65%, 100% 65%, 100% 75%, 0 75%);
    }
    75% {
        transform: translateX(-1px);
        clip-path: polygon(0 75%, 100% 75%, 100% 85%, 0 85%);
    }
    100% {
        transform: translateX(0);
        clip-path: polygon(0 0%, 100% 0%, 100% 100%, 0 100%);
    }
}

/* Intense glitch effect */
.glitch-intense {
    animation: glitch-intense 0.3s infinite;
}

@keyframes glitch-intense {
    0% {
        transform: translateX(0) skew(0deg);
        filter: hue-rotate(0deg);
    }
    10% {
        transform: translateX(-2px) skew(-2deg);
        filter: hue-rotate(90deg);
    }
    20% {
        transform: translateX(2px) skew(2deg);
        filter: hue-rotate(180deg);
    }
    30% {
        transform: translateX(-1px) skew(-1deg);
        filter: hue-rotate(270deg);
    }
    40% {
        transform: translateX(1px) skew(1deg);
        filter: hue-rotate(360deg);
    }
    50% {
        transform: translateX(-3px) skew(-3deg);
        filter: hue-rotate(90deg) contrast(150%);
    }
    60% {
        transform: translateX(3px) skew(3deg);
        filter: hue-rotate(180deg) contrast(200%);
    }
    70% {
        transform: translateX(-1px) skew(-1deg);
        filter: hue-rotate(270deg) contrast(100%);
    }
    80% {
        transform: translateX(2px) skew(2deg);
        filter: hue-rotate(360deg) brightness(150%);
    }
    90% {
        transform: translateX(-2px) skew(-2deg);
        filter: hue-rotate(45deg) brightness(100%);
    }
    100% {
        transform: translateX(0) skew(0deg);
        filter: hue-rotate(0deg);
    }
}

/* RGB Split Effect */
.glitch-rgb {
    position: relative;
}

.glitch-rgb::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    color: #ff0000;
    mix-blend-mode: screen;
    animation: glitch-rgb-1 0.6s infinite;
}

.glitch-rgb::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    color: #00ffff;
    mix-blend-mode: screen;
    animation: glitch-rgb-2 0.6s infinite;
}

@keyframes glitch-rgb-1 {
    0% { transform: translateX(0); }
    20% { transform: translateX(-2px); }
    40% { transform: translateX(2px); }
    60% { transform: translateX(-1px); }
    80% { transform: translateX(1px); }
    100% { transform: translateX(0); }
}

@keyframes glitch-rgb-2 {
    0% { transform: translateX(0); }
    25% { transform: translateX(2px); }
    50% { transform: translateX(-2px); }
    75% { transform: translateX(1px); }
    100% { transform: translateX(0); }
}

/* Scanline Effect */
.glitch-scanlines {
    position: relative;
    overflow: hidden;
}

.glitch-scanlines::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 2px,
        rgba(255, 255, 255, 0.03) 2px,
        rgba(255, 255, 255, 0.03) 4px
    );
    pointer-events: none;
    animation: scanlines 0.1s linear infinite;
}

@keyframes scanlines {
    0% { transform: translateY(0); }
    100% { transform: translateY(4px); }
}

/* Digital Noise Effect */
.glitch-noise {
    position: relative;
}

.glitch-noise::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, transparent 20%, rgba(255,255,255,0.3) 21%, rgba(255,255,255,0.3) 34%, transparent 35%),
        linear-gradient(0deg, transparent 24%, rgba(255,255,255,0.05) 25%, rgba(255,255,255,0.05) 26%, transparent 27%, transparent 74%, rgba(255,255,255,0.05) 75%, rgba(255,255,255,0.05) 76%, transparent 77%);
    background-size: 50px 50px, 100% 100%;
    opacity: 0.3;
    animation: noise 0.2s infinite;
    pointer-events: none;
}

@keyframes noise {
    0% { transform: translateX(0px) translateY(0px); }
    10% { transform: translateX(-1px) translateY(-1px); }
    20% { transform: translateX(1px) translateY(0px); }
    30% { transform: translateX(0px) translateY(1px); }
    40% { transform: translateX(-1px) translateY(0px); }
    50% { transform: translateX(1px) translateY(1px); }
    60% { transform: translateX(0px) translateY(-1px); }
    70% { transform: translateX(-1px) translateY(1px); }
    80% { transform: translateX(1px) translateY(0px); }
    90% { transform: translateX(0px) translateY(-1px); }
    100% { transform: translateX(-1px) translateY(0px); }
}

/* Terminal-specific glitch effects */
.terminal-glitch {
    animation: terminal-glitch 2s infinite;
}

@keyframes terminal-glitch {
    0%, 90%, 100% {
        transform: translateX(0);
        filter: hue-rotate(0deg);
    }
    91% {
        transform: translateX(-2px);
        filter: hue-rotate(90deg);
    }
    92% {
        transform: translateX(2px);
        filter: hue-rotate(180deg);
    }
    93% {
        transform: translateX(-1px);
        filter: hue-rotate(270deg);
    }
    94% {
        transform: translateX(1px);
        filter: hue-rotate(360deg);
    }
    95% {
        transform: translateX(0);
        filter: hue-rotate(0deg);
    }
}

/* Glitch trigger classes */
.glitch-trigger:hover .glitch {
    animation-duration: 0.1s;
}

.glitch-active {
    animation: glitch-intense 0.5s ease-in-out;
}

/* Theme-specific glitch colors */
.theme-matrix .glitch::before { color: #00ff41; }
.theme-matrix .glitch::after { color: #008f11; }

.theme-cyberpunk .glitch::before { color: #ff0080; }
.theme-cyberpunk .glitch::after { color: #00ffff; }

.theme-retro .glitch::before { color: #ffaa00; }
.theme-retro .glitch::after { color: #ff6600; }

.theme-hacker .glitch::before { color: #ff3333; }
.theme-hacker .glitch::after { color: #990000; }

/* Performance optimizations */
.glitch,
.glitch::before,
.glitch::after {
    will-change: transform, filter;
    transform: translateZ(0);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .glitch,
    .glitch-intense,
    .glitch-rgb,
    .terminal-glitch {
        animation: none;
    }
    
    .glitch::before,
    .glitch::after {
        animation: none;
        opacity: 0.3;
    }
}
