// Interactive Terminal System for CGG
class InteractiveTerminal {
    constructor() {
        this.terminal = document.getElementById('interactive-terminal');
        this.content = document.getElementById('terminal-content');
        this.input = document.getElementById('terminal-input');
        this.isOpen = false;
        this.commandHistory = [];
        this.historyIndex = -1;
        this.currentPath = '/home/<USER>';
        this.username = 'guest';
        this.hostname = 'cgg';

        this.fileSystem = {
            '/': {
                type: 'directory',
                contents: {
                    'home': {
                        type: 'directory',
                        contents: {
                            'guest': {
                                type: 'directory',
                                contents: {
                                    'documents': { type: 'directory', contents: {} },
                                    'projects': { type: 'directory', contents: {
                                        'cgg-website': { type: 'directory', contents: {} },
                                        'security-tools': { type: 'directory', contents: {} }
                                    }},
                                    'readme.txt': { type: 'file', content: 'Welcome to CGG Terminal!\nType "help" for available commands.' }
                                }
                            }
                        }
                    },
                    'var': {
                        type: 'directory',
                        contents: {
                            'log': { type: 'directory', contents: {
                                'security.log': { type: 'file', content: '[2024-12-15 10:30:15] Security scan completed - No threats detected\n[2024-12-15 10:25:10] Firewall status: ACTIVE\n[2024-12-15 10:20:05] System startup completed' }
                            }}
                        }
                    },
                    'etc': {
                        type: 'directory',
                        contents: {
                            'cgg-config': { type: 'file', content: 'CGG Security Configuration\n========================\nVersion: 3.0.0\nSecurity Level: Maximum\nEncryption: AES-256\nFirewall: Enabled' }
                        }
                    }
                }
            }
        };

        this.commands = {
            help: this.showHelp.bind(this),
            clear: this.clearTerminal.bind(this),
            ls: this.listFiles.bind(this),
            cd: this.changeDirectory.bind(this),
            pwd: this.printWorkingDirectory.bind(this),
            cat: this.showFileContent.bind(this),
            whoami: this.showUser.bind(this),
            date: this.showDate.bind(this),
            uptime: this.showUptime.bind(this),
            ps: this.showProcesses.bind(this),
            top: this.showSystemInfo.bind(this),
            neofetch: this.showSystemInfo.bind(this),
            cgg: this.showCGGInfo.bind(this),
            services: this.showServices.bind(this),
            projects: this.showProjects.bind(this),
            team: this.showTeam.bind(this),
            contact: this.showContact.bind(this),
            blog: this.showBlog.bind(this),
            navigate: this.navigateToPage.bind(this),
            login: this.adminLogin.bind(this),
            admin: this.adminAccess.bind(this),
            scan: this.runSecurityScan.bind(this),
            encrypt: this.encryptData.bind(this),
            decrypt: this.decryptData.bind(this),
            firewall: this.firewallStatus.bind(this),
            matrix: this.startMatrix.bind(this),
            game: this.startGame.bind(this),
            hack: this.hackingSimulation.bind(this),
            exit: this.closeTerminal.bind(this),
            sudo: this.sudoCommand.bind(this),
            history: this.showHistory.bind(this),
            echo: this.echoCommand.bind(this),
            ping: this.pingCommand.bind(this),
            curl: this.curlCommand.bind(this),
            ssh: this.sshCommand.bind(this),
            nmap: this.nmapCommand.bind(this)
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updatePrompt();
    }

    setupEventListeners() {
        // Terminal input handling
        this.input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.executeCommand();
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                this.navigateHistory(-1);
            } else if (e.key === 'ArrowDown') {
                e.preventDefault();
                this.navigateHistory(1);
            } else if (e.key === 'Tab') {
                e.preventDefault();
                this.autoComplete();
            }
        });

        // Terminal controls
        document.getElementById('terminal-close').addEventListener('click', () => {
            this.closeTerminal();
        });

        document.getElementById('terminal-maximize').addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // Click outside to close
        this.terminal.addEventListener('click', (e) => {
            if (e.target === this.terminal) {
                this.closeTerminal();
            }
        });
    }

    openTerminal() {
        this.isOpen = true;
        this.terminal.classList.add('active');
        this.input.focus();
        this.addOutput('Terminal initialized. Type "help" for available commands.', 'terminal-success');
    }

    closeTerminal() {
        this.isOpen = false;
        this.terminal.classList.remove('active');
    }

    toggleFullscreen() {
        this.terminal.classList.toggle('fullscreen');
    }

    updatePrompt() {
        const prompt = document.querySelector('.terminal-prompt');
        if (prompt) {
            prompt.textContent = `${this.username}@${this.hostname}:${this.currentPath}$`;
        }
    }

    executeCommand() {
        const command = this.input.value.trim();
        if (!command) return;

        // Add command to history
        this.commandHistory.push(command);
        this.historyIndex = this.commandHistory.length;

        // Display command
        this.addOutput(`${this.username}@${this.hostname}:${this.currentPath}$ ${command}`, 'terminal-info');

        // Parse and execute command
        const [cmd, ...args] = command.split(' ');

        if (this.commands[cmd]) {
            this.commands[cmd](args);
        } else {
            this.addOutput(`Command not found: ${cmd}. Type "help" for available commands.`, 'terminal-error');
        }

        // Clear input
        this.input.value = '';
        this.scrollToBottom();
    }

    addOutput(text, className = 'terminal-output') {
        const line = document.createElement('div');
        line.className = `terminal-line ${className}`;
        line.innerHTML = text;
        this.content.appendChild(line);
    }

    scrollToBottom() {
        this.content.scrollTop = this.content.scrollHeight;
    }

    navigateHistory(direction) {
        if (direction === -1 && this.historyIndex > 0) {
            this.historyIndex--;
            this.input.value = this.commandHistory[this.historyIndex];
        } else if (direction === 1 && this.historyIndex < this.commandHistory.length - 1) {
            this.historyIndex++;
            this.input.value = this.commandHistory[this.historyIndex];
        } else if (direction === 1 && this.historyIndex === this.commandHistory.length - 1) {
            this.historyIndex = this.commandHistory.length;
            this.input.value = '';
        }
    }

    autoComplete() {
        const input = this.input.value;
        const commands = Object.keys(this.commands);
        const matches = commands.filter(cmd => cmd.startsWith(input));

        if (matches.length === 1) {
            this.input.value = matches[0];
        } else if (matches.length > 1) {
            this.addOutput(`Available commands: ${matches.join(', ')}`, 'terminal-info');
        }
    }

    // Command implementations
    showHelp() {
        const helpText = `
<div class="cmd-help">
<div class="cmd-header">CGG Terminal - Available Commands</div>

<div class="cmd-section">
<div class="cmd-command">Navigation & File System:</div>
<div class="cmd-description">ls [path]          - List directory contents</div>
<div class="cmd-description">cd [directory]     - Change directory</div>
<div class="cmd-description">pwd               - Print working directory</div>
<div class="cmd-description">cat [file]        - Display file contents</div>
</div>

<div class="cmd-section">
<div class="cmd-command">System Information:</div>
<div class="cmd-description">whoami            - Display current user</div>
<div class="cmd-description">date              - Show current date/time</div>
<div class="cmd-description">uptime            - Show system uptime</div>
<div class="cmd-description">ps                - Show running processes</div>
<div class="cmd-description">top/neofetch      - Show system information</div>
</div>

<div class="cmd-section">
<div class="cmd-command">CGG Specific:</div>
<div class="cmd-description">cgg               - Show CGG information</div>
<div class="cmd-description">services          - List our services</div>
<div class="cmd-description">projects          - Show our projects</div>
<div class="cmd-description">team              - Meet our team</div>
<div class="cmd-description">contact           - Contact information</div>
<div class="cmd-description">navigate [page]   - Navigate to website page</div>
</div>

<div class="cmd-section">
<div class="cmd-command">Admin Access:</div>
<div class="cmd-description">admin             - Show admin access info</div>
<div class="cmd-description">login [user] [pass] - Admin authentication</div>
<div class="cmd-description">navigate login    - Go to login page</div>
</div>

<div class="cmd-section">
<div class="cmd-command">Security Tools:</div>
<div class="cmd-description">scan              - Run security scan</div>
<div class="cmd-description">encrypt [text]    - Encrypt data</div>
<div class="cmd-description">decrypt [hash]    - Decrypt data</div>
<div class="cmd-description">firewall          - Check firewall status</div>
<div class="cmd-description">nmap [target]     - Network scan</div>
<div class="cmd-description">ping [host]       - Ping a host</div>
</div>

<div class="cmd-section">
<div class="cmd-command">Fun & Games:</div>
<div class="cmd-description">matrix            - Start matrix animation</div>
<div class="cmd-description">game              - Play tic-tac-toe</div>
<div class="cmd-description">hack              - Hacking simulation</div>
</div>

<div class="cmd-section">
<div class="cmd-command">Utilities:</div>
<div class="cmd-description">clear             - Clear terminal</div>
<div class="cmd-description">history           - Show command history</div>
<div class="cmd-description">echo [text]       - Display text</div>
<div class="cmd-description">exit              - Close terminal</div>
</div>

<div class="cmd-example">Use Tab for auto-completion, ↑↓ for command history</div>
</div>`;
        this.addOutput(helpText);
    }

    clearTerminal() {
        this.content.innerHTML = '';
    }

    listFiles(args) {
        const path = args[0] || this.currentPath;
        const dir = this.getDirectoryContents(path);

        if (!dir) {
            this.addOutput(`ls: cannot access '${path}': No such file or directory`, 'terminal-error');
            return;
        }

        const files = Object.entries(dir).map(([name, info]) => {
            const icon = info.type === 'directory' ? '📁' : '📄';
            return `<div class="file-item"><span class="file-icon">${icon}</span><span class="file-name">${name}</span></div>`;
        }).join('');

        this.addOutput(`<div class="file-list">${files}</div>`);
    }

    changeDirectory(args) {
        if (!args[0]) {
            this.currentPath = '/home/<USER>';
            this.updatePrompt();
            return;
        }

        const newPath = this.resolvePath(args[0]);
        const dir = this.getDirectoryContents(newPath);

        if (!dir) {
            this.addOutput(`cd: no such file or directory: ${args[0]}`, 'terminal-error');
            return;
        }

        this.currentPath = newPath;
        this.updatePrompt();
    }

    printWorkingDirectory() {
        this.addOutput(this.currentPath);
    }

    showFileContent(args) {
        if (!args[0]) {
            this.addOutput('cat: missing file operand', 'terminal-error');
            return;
        }

        const filePath = this.resolvePath(args[0]);
        const file = this.getFileContent(filePath);

        if (!file) {
            this.addOutput(`cat: ${args[0]}: No such file or directory`, 'terminal-error');
            return;
        }

        if (file.type !== 'file') {
            this.addOutput(`cat: ${args[0]}: Is a directory`, 'terminal-error');
            return;
        }

        this.addOutput(file['content'] || 'File is empty');
    }

    showUser() {
        this.addOutput(this.username);
    }

    showDate() {
        const now = new Date();
        this.addOutput(now.toString());
    }

    showUptime() {
        const uptime = Math.floor(Math.random() * 1000000);
        this.addOutput(`System uptime: ${uptime} seconds`);
    }

    showProcesses() {
        const processes = [
            'PID  COMMAND',
            '1    systemd',
            '2    kthreadd',
            '3    rcu_gp',
            '1337 cgg-security-daemon',
            '1338 cgg-firewall',
            '1339 cgg-monitor',
            '2048 nginx',
            '2049 mysql'
        ];
        this.addOutput(processes.join('\n'));
    }

    showSystemInfo() {
        const info = `
<div class="system-info">
<div class="info-card">
<div class="info-title">System Information</div>
<div class="info-content">
OS: CGG SecureOS v3.0.0<br>
Kernel: Linux 5.15.0-cgg<br>
Architecture: x86_64<br>
Uptime: 15 days, 7 hours
</div>
</div>
<div class="info-card">
<div class="info-title">Security Status</div>
<div class="info-content">
Firewall: ✅ Active<br>
Antivirus: ✅ Running<br>
Encryption: ✅ AES-256<br>
Threats Blocked: 1,247
</div>
</div>
<div class="info-card">
<div class="info-title">Performance</div>
<div class="info-content">
CPU Usage: 12%<br>
Memory: 2.1GB / 16GB<br>
Disk: 45GB / 500GB<br>
Network: 1Gbps
</div>
</div>
</div>`;
        this.addOutput(info);
    }

    showCGGInfo() {
        const cggInfo = `
<div class="cmd-help">
<div class="cmd-header">🛡️ Code Guard Group (CGG) 🛡️</div>
<div class="cmd-description">
<strong>Mission:</strong> Securing the Digital Future<br>
<strong>Founded:</strong> 2020<br>
<strong>Headquarters:</strong> Cyber Security City<br>
<strong>Employees:</strong> 50+ Security Experts<br><br>

<strong>Core Values:</strong><br>
• Innovation in Cybersecurity<br>
• 24/7 Protection<br>
• Zero-Trust Architecture<br>
• Advanced Threat Intelligence<br><br>

<strong>Certifications:</strong><br>
• ISO 27001 Certified<br>
• SOC 2 Type II Compliant<br>
• GDPR Compliant<br>
• PCI DSS Level 1<br>
</div>
</div>`;
        this.addOutput(cggInfo);
    }

    showServices() {
        const services = `
<div class="cmd-help">
<div class="cmd-header">🔒 CGG Security Services</div>
<div class="cmd-section">
<div class="cmd-command">1. Threat Detection & Prevention</div>
<div class="cmd-description">• AI-powered threat analysis</div>
<div class="cmd-description">• Real-time monitoring</div>
<div class="cmd-description">• Zero-day protection</div>
</div>
<div class="cmd-section">
<div class="cmd-command">2. Data Encryption Services</div>
<div class="cmd-description">• AES-256 encryption</div>
<div class="cmd-description">• Key management</div>
<div class="cmd-description">• Secure data transmission</div>
</div>
<div class="cmd-section">
<div class="cmd-command">3. Network Security</div>
<div class="cmd-description">• Firewall management</div>
<div class="cmd-description">• Intrusion detection</div>
<div class="cmd-description">• VPN solutions</div>
</div>
<div class="cmd-section">
<div class="cmd-command">4. Identity Management</div>
<div class="cmd-description">• Multi-factor authentication</div>
<div class="cmd-description">• Single sign-on</div>
<div class="cmd-description">• Privilege management</div>
</div>
</div>`;
        this.addOutput(services);
    }

    showProjects() {
        const projects = `
<div class="cmd-help">
<div class="cmd-header">💼 Recent Projects</div>
<div class="cmd-section">
<div class="cmd-command">SecureBank Platform</div>
<div class="cmd-description">Complete cybersecurity overhaul for major banking institution</div>
<div class="cmd-example">Technologies: Banking, Encryption, Compliance</div>
</div>
<div class="cmd-section">
<div class="cmd-command">HealthGuard System</div>
<div class="cmd-description">HIPAA-compliant security solution for healthcare networks</div>
<div class="cmd-example">Technologies: Healthcare, HIPAA, Privacy</div>
</div>
<div class="cmd-section">
<div class="cmd-command">EduSecure Network</div>
<div class="cmd-description">Educational institution network security implementation</div>
<div class="cmd-example">Technologies: Education, Network, Monitoring</div>
</div>
</div>`;
        this.addOutput(projects);
    }

    showTeam() {
        const team = `
<div class="cmd-help">
<div class="cmd-header">👥 Our Expert Team</div>
<div class="cmd-section">
<div class="cmd-command">Ahmed Al-Rashid - Chief Security Officer</div>
<div class="cmd-description">15+ years in cybersecurity with expertise in threat intelligence</div>
</div>
<div class="cmd-section">
<div class="cmd-command">Sarah Johnson - Lead Security Engineer</div>
<div class="cmd-description">Specialist in network security and penetration testing</div>
</div>
<div class="cmd-section">
<div class="cmd-command">محمد العلي - Cryptography Specialist</div>
<div class="cmd-description">Expert in encryption algorithms and secure communications</div>
</div>
</div>`;
        this.addOutput(team);
    }

    showContact() {
        const contact = `
<div class="cmd-help">
<div class="cmd-header">📞 Contact Information</div>
<div class="cmd-description">
<strong>Address:</strong> 123 Cyber Street, Security City, SC 12345<br>
<strong>Phone:</strong> +****************<br>
<strong>Email:</strong> <EMAIL><br>
<strong>Website:</strong> www.codeGuardGroup.com<br><br>

<strong>Business Hours:</strong><br>
Monday - Friday: 9:00 AM - 6:00 PM<br>
Saturday: 10:00 AM - 4:00 PM<br>
Sunday: Emergency Support Only<br><br>

<strong>Emergency Hotline:</strong> +1 (555) 911-HELP
</div>
</div>`;
        this.addOutput(contact);
    }

    showBlog() {
        const blog = `
<div class="cmd-help">
<div class="cmd-header">📝 Latest Blog Posts</div>
<div class="cmd-section">
<div class="cmd-command">The Rise of AI-Powered Cyber Attacks</div>
<div class="cmd-description">How artificial intelligence is being weaponized by cybercriminals</div>
<div class="cmd-example">Published: Dec 15, 2024 | Category: Threat Intelligence</div>
</div>
<div class="cmd-section">
<div class="cmd-command">Zero Trust Architecture Implementation</div>
<div class="cmd-description">A comprehensive guide to implementing zero trust security</div>
<div class="cmd-example">Published: Dec 10, 2024 | Category: Best Practices</div>
</div>
<div class="cmd-section">
<div class="cmd-command">GDPR Compliance in 2024</div>
<div class="cmd-description">Updated guidelines and best practices for maintaining GDPR compliance</div>
<div class="cmd-example">Published: Dec 5, 2024 | Category: Compliance</div>
</div>
</div>`;
        this.addOutput(blog);
    }

    navigateToPage(args) {
        if (!args[0]) {
            this.addOutput('Usage: navigate [page]', 'terminal-error');
            this.addOutput('Available pages: home, services, projects, team, blog, contact', 'terminal-info');
            return;
        }

        const page = args[0].toLowerCase();
        const validPages = ['home', 'services', 'projects', 'team', 'blog', 'contact'];

        if (!validPages.includes(page)) {
            this.addOutput(`Invalid page: ${page}`, 'terminal-error');
            this.addOutput(`Available pages: ${validPages.join(', ')}`, 'terminal-info');
            return;
        }

        this.addOutput(`Navigating to ${page} page...`, 'terminal-success');

        // Trigger page navigation
        setTimeout(() => {
            const navLink = document.querySelector(`[data-page="${page}"]`);
            if (navLink) {
                navLink.click();
                this.closeTerminal();
            }
        }, 1000);
    }

    runSecurityScan() {
        this.addOutput('Initiating comprehensive security scan...', 'terminal-info');

        const scanSteps = [
            'Scanning network interfaces...',
            'Checking firewall rules...',
            'Analyzing running processes...',
            'Scanning for malware signatures...',
            'Checking system vulnerabilities...',
            'Validating encryption protocols...',
            'Scan completed successfully!'
        ];

        let step = 0;
        const interval = setInterval(() => {
            if (step < scanSteps.length - 1) {
                this.addOutput(`[${step + 1}/6] ${scanSteps[step]}`, 'terminal-info');
                step++;
            } else {
                this.addOutput(scanSteps[step], 'terminal-success');
                this.addOutput('✅ No threats detected. System is secure.', 'terminal-success');
                clearInterval(interval);
            }
        }, 800);
    }

    encryptData(args) {
        if (!args[0]) {
            this.addOutput('Usage: encrypt [text]', 'terminal-error');
            return;
        }

        const text = args.join(' ');
        const encrypted = btoa(text).split('').reverse().join('');

        this.addOutput('Encrypting data using AES-256...', 'terminal-info');
        setTimeout(() => {
            this.addOutput(`Encrypted: ${encrypted}`, 'terminal-success');
        }, 1000);
    }

    decryptData(args) {
        if (!args[0]) {
            this.addOutput('Usage: decrypt [hash]', 'terminal-error');
            return;
        }

        try {
            const hash = args[0];
            const decrypted = atob(hash.split('').reverse().join(''));

            this.addOutput('Decrypting data...', 'terminal-info');
            setTimeout(() => {
                this.addOutput(`Decrypted: ${decrypted}`, 'terminal-success');
            }, 1000);
        } catch (e) {
            this.addOutput('Invalid hash format', 'terminal-error');
        }
    }

    firewallStatus() {
        const status = `
<div class="cmd-help">
<div class="cmd-header">🔥 Firewall Status</div>
<div class="cmd-description">
<strong>Status:</strong> ✅ ACTIVE<br>
<strong>Rules:</strong> 247 active rules<br>
<strong>Blocked IPs:</strong> 1,337<br>
<strong>Allowed Connections:</strong> 42<br>
<strong>Last Update:</strong> 2024-12-15 10:30:15<br><br>

<strong>Recent Blocks:</strong><br>
• ***********00 - Suspicious activity<br>
• ********* - Port scanning attempt<br>
• *********** - Malware signature detected<br>
</div>
</div>`;
        this.addOutput(status);
    }

    startMatrix() {
        this.addOutput('Initializing Matrix visualization...', 'terminal-success');
        this.addOutput('Press Ctrl+C to stop', 'terminal-info');

        // Simple matrix effect
        let count = 0;
        const interval = setInterval(() => {
            const chars = '01CGG';
            const line = Array(50).fill().map(() => chars[Math.floor(Math.random() * chars.length)]).join(' ');
            this.addOutput(`<span style="color: #00ff41; opacity: ${Math.random()}">${line}</span>`);
            count++;

            if (count > 10) {
                clearInterval(interval);
                this.addOutput('Matrix simulation ended.', 'terminal-info');
            }
        }, 200);
    }

    startGame() {
        this.addOutput('Starting Tic-Tac-Toe game...', 'terminal-success');
        this.addOutput('Click on cells to play. You are X, computer is O.', 'terminal-info');

        const board = Array(9).fill('');
        const gameBoard = `
<div class="game-board" style="grid-template-columns: repeat(3, 1fr);">
${board.map((cell, index) => `<div class="game-cell" onclick="window.terminal.makeMove(${index})">${cell}</div>`).join('')}
</div>`;

        this.addOutput(gameBoard);
        this.gameBoard = board;
    }

    makeMove(index) {
        if (this.gameBoard[index] !== '') return;

        this.gameBoard[index] = 'X';
        this.updateGameBoard();

        // Simple AI move
        setTimeout(() => {
            const emptyCells = this.gameBoard.map((cell, i) => cell === '' ? i : null).filter(i => i !== null);
            if (emptyCells.length > 0) {
                const aiMove = emptyCells[Math.floor(Math.random() * emptyCells.length)];
                this.gameBoard[aiMove] = 'O';
                this.updateGameBoard();
            }
        }, 500);
    }

    updateGameBoard() {
        const cells = document.querySelectorAll('.game-cell');
        cells.forEach((cell, index) => {
            cell.textContent = this.gameBoard[index];
            cell.className = `game-cell ${this.gameBoard[index].toLowerCase()}`;
        });
    }

    hackingSimulation() {
        this.addOutput('🚨 UNAUTHORIZED ACCESS DETECTED 🚨', 'terminal-error');
        this.addOutput('Just kidding! This is a simulation 😄', 'terminal-success');

        const hackSteps = [
            'Scanning target system...',
            'Exploiting buffer overflow...',
            'Escalating privileges...',
            'Installing backdoor...',
            'Covering tracks...',
            'Access granted! (Not really)'
        ];

        let step = 0;
        const interval = setInterval(() => {
            if (step < hackSteps.length) {
                this.addOutput(`[HACK] ${hackSteps[step]}`, 'terminal-warning');
                step++;
            } else {
                this.addOutput('Simulation complete. Remember: Use your powers for good! 🛡️', 'terminal-success');
                clearInterval(interval);
            }
        }, 1000);
    }

    sudoCommand(args) {
        if (!args[0]) {
            this.addOutput('sudo: a command is required', 'terminal-error');
            return;
        }

        this.addOutput('[sudo] password for guest: ', 'terminal-warning');
        this.addOutput('Authentication successful', 'terminal-success');

        const [cmd, ...cmdArgs] = args;
        if (this.commands[cmd]) {
            this.commands[cmd](cmdArgs);
        } else {
            this.addOutput(`sudo: ${cmd}: command not found`, 'terminal-error');
        }
    }

    showHistory() {
        this.addOutput('Command History:', 'terminal-info');
        this.commandHistory.forEach((cmd, index) => {
            this.addOutput(`${index + 1}  ${cmd}`);
        });
    }

    echoCommand(args) {
        this.addOutput(args.join(' '));
    }

    pingCommand(args) {
        if (!args[0]) {
            this.addOutput('Usage: ping [host]', 'terminal-error');
            return;
        }

        const host = args[0];
        this.addOutput(`PING ${host} (***********): 56 data bytes`, 'terminal-info');

        for (let i = 1; i <= 4; i++) {
            setTimeout(() => {
                const time = (Math.random() * 50 + 10).toFixed(1);
                this.addOutput(`64 bytes from ${host}: icmp_seq=${i} time=${time}ms`);
            }, i * 1000);
        }
    }

    curlCommand(args) {
        if (!args[0]) {
            this.addOutput('Usage: curl [url]', 'terminal-error');
            return;
        }

        const url = args[0];
        this.addOutput(`Fetching ${url}...`, 'terminal-info');

        setTimeout(() => {
            this.addOutput('HTTP/1.1 200 OK', 'terminal-success');
            this.addOutput('Content-Type: application/json', 'terminal-info');
            this.addOutput('{"status": "success", "message": "CGG API endpoint"}');
        }, 1500);
    }

    sshCommand(args) {
        if (!args[0]) {
            this.addOutput('Usage: ssh [user@host]', 'terminal-error');
            return;
        }

        const target = args[0];
        this.addOutput(`Connecting to ${target}...`, 'terminal-info');

        setTimeout(() => {
            this.addOutput('Connection established', 'terminal-success');
            this.addOutput('Welcome to CGG Secure Server!', 'terminal-success');
        }, 2000);
    }

    nmapCommand(args) {
        if (!args[0]) {
            this.addOutput('Usage: nmap [target]', 'terminal-error');
            return;
        }

        const target = args[0];
        this.addOutput(`Starting Nmap scan on ${target}...`, 'terminal-info');

        setTimeout(() => {
            const ports = [
                '22/tcp   open  ssh',
                '80/tcp   open  http',
                '443/tcp  open  https',
                '3306/tcp closed mysql'
            ];

            this.addOutput('PORT     STATE  SERVICE', 'terminal-info');
            ports.forEach(port => {
                this.addOutput(port);
            });
            this.addOutput('Nmap scan completed', 'terminal-success');
        }, 3000);
    }

    adminLogin(args) {
        if (!args[0] || !args[1]) {
            this.addOutput('Usage: login [username] [password]', 'terminal-error');
            this.addOutput('Example: login Mossef1 yourpassword', 'terminal-info');
            return;
        }

        const username = args[0];
        const password = args[1];

        this.addOutput('🔐 Attempting admin authentication...', 'terminal-info');

        setTimeout(() => {
            if (window.adminManager) {
                // Simulate login through admin manager
                const isValid = username === 'Mossef1' && password === 'CGG@2024!Secure';

                if (isValid) {
                    this.addOutput('✅ Authentication successful!', 'terminal-success');
                    this.addOutput('🚀 Redirecting to admin dashboard...', 'terminal-info');

                    setTimeout(() => {
                        window.adminManager.isLoggedIn = true;
                        window.adminManager.currentUser = username;
                        window.adminManager.showDashboard();
                        this.closeTerminal();
                    }, 1500);
                } else {
                    this.addOutput('❌ Authentication failed!', 'terminal-error');
                    this.addOutput('Invalid credentials. Access denied.', 'terminal-error');
                }
            } else {
                this.addOutput('❌ Admin system not available', 'terminal-error');
            }
        }, 2000);
    }

    adminAccess() {
        this.addOutput('🛡️ CGG Admin Access Portal', 'terminal-info');
        this.addOutput('', '');
        this.addOutput('Available admin commands:', 'terminal-success');
        this.addOutput('• login [username] [password] - Admin authentication', 'terminal-info');
        this.addOutput('• navigate login - Go to login page', 'terminal-info');
        this.addOutput('', '');
        this.addOutput('Security Notice:', 'terminal-warning');
        this.addOutput('All admin access attempts are logged and monitored.', 'terminal-warning');
        this.addOutput('Unauthorized access is strictly prohibited.', 'terminal-warning');
        this.addOutput('', '');
        this.addOutput('For admin access, use: login Mossef1 [password]', 'terminal-info');
    }

    // Helper methods
    resolvePath(path) {
        if (path.startsWith('/')) {
            return path;
        }
        return `${this.currentPath}/${path}`.replace(/\/+/g, '/');
    }

    getDirectoryContents(path) {
        const parts = path.split('/').filter(p => p);
        let current = this.fileSystem['/'];

        for (const part of parts) {
            if (current.type !== 'directory' || !current.contents[part]) {
                return null;
            }
            current = current.contents[part];
        }

        return current.type === 'directory' ? current.contents : null;
    }

    getFileContent(path) {
        const parts = path.split('/').filter(p => p);
        let current = this.fileSystem['/'];

        for (const part of parts) {
            if (current.type !== 'directory' || !current.contents[part]) {
                return null;
            }
            current = current.contents[part];
        }

        return current;
    }
}

// Initialize terminal when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.terminal = new InteractiveTerminal();
});
