/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: #000;
    color: #00ff41;
    overflow-x: hidden;
    min-height: 100vh;
    position: relative;
}

.container {
    width: 100vw;
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: radial-gradient(ellipse at center, #001122 0%, #000000 70%);
}

/* Home Page Styles */
#page-home {
    background: #000000;
    position: relative;
    overflow: hidden;
    padding: 0;
    margin: 0;
    width: 100vw;
    height: 100vh;
}



/* Glitch Effects */
.glitch-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    z-index: 15;
    pointer-events: none;
}

.glitch-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 2px,
        rgba(255, 0, 0, 0.1) 2px,
        rgba(255, 0, 0, 0.1) 4px
    );
    animation: glitchLines 0.1s infinite;
}

.glitch-noise {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><filter id="noise"><feTurbulence baseFrequency="0.9" numOctaves="4" stitchTiles="stitch"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/></filter></defs><rect width="100%" height="100%" filter="url(%23noise)" opacity="0.4"/></svg>');
    animation: glitchNoise 0.2s infinite;
}

/* Code Canvas */
.code-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 5;
    opacity: 0;
}

/* Logo Container */
.logo-container {
    position: relative;
    z-index: 20;
    opacity: 0;
    transform: scale(0.5);
    animation: logoAppear 2s ease-out 4s forwards;
}

/* CGG Logo */
.cgg-logo {
    display: flex;
    gap: 20px;
    margin-bottom: 40px;
    justify-content: center;
}

.logo-letter {
    font-size: 120px;
    font-weight: 900;
    color: transparent;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #00ffff);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    text-shadow:
        0 0 20px rgba(0, 255, 255, 0.5),
        0 0 40px rgba(255, 0, 255, 0.3),
        0 0 60px rgba(0, 255, 255, 0.2);
    animation:
        holographicGlow 3s ease-in-out infinite,
        letterFloat 4s ease-in-out infinite;
    transform-origin: center;
}

.logo-letter:nth-child(1) { animation-delay: 0s, 0s; }
.logo-letter:nth-child(2) { animation-delay: 0.2s, 0.5s; }
.logo-letter:nth-child(3) { animation-delay: 0.4s, 1s; }

/* Digital Shield */
.digital-shield {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    height: 400px;
    opacity: 0;
    animation: shieldAppear 1.5s ease-out 6s forwards;
}

.shield-layer {
    position: absolute;
    border: 2px solid;
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    animation: shieldRotate 10s linear infinite;
}

.shield-outer {
    width: 100%;
    height: 100%;
    border-color: rgba(0, 255, 255, 0.6);
    animation-duration: 15s;
}

.shield-middle {
    width: 80%;
    height: 80%;
    top: 10%;
    left: 10%;
    border-color: rgba(255, 0, 255, 0.6);
    animation-duration: 12s;
    animation-direction: reverse;
}

.shield-inner {
    width: 60%;
    height: 60%;
    top: 20%;
    left: 20%;
    border-color: rgba(0, 255, 65, 0.6);
    animation-duration: 8s;
}

/* Circuit Lines */
.shield-circuits {
    position: absolute;
    width: 100%;
    height: 100%;
}

.circuit-line {
    position: absolute;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    height: 2px;
    animation: circuitPulse 2s ease-in-out infinite;
}

.circuit-1 {
    top: 25%;
    left: 0;
    width: 100%;
    animation-delay: 0s;
}

.circuit-2 {
    top: 50%;
    left: 0;
    width: 100%;
    animation-delay: 0.5s;
}

.circuit-3 {
    top: 75%;
    left: 0;
    width: 100%;
    animation-delay: 1s;
}

.circuit-4 {
    top: 0;
    left: 50%;
    width: 2px;
    height: 100%;
    background: linear-gradient(0deg, transparent, #ff00ff, transparent);
    animation-delay: 1.5s;
}

/* Company Text */
.company-text {
    text-align: center;
    margin-top: 60px;
    opacity: 0;
    animation: textAppear 1s ease-out 8s forwards;
}

.text-english {
    font-size: 32px;
    font-weight: 700;
    color: #00ffff;
    text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
    margin-bottom: 10px;
    letter-spacing: 3px;
}

.text-arabic {
    font-family: 'Rajdhani', sans-serif;
    font-size: 24px;
    font-weight: 600;
    color: #ff00ff;
    text-shadow: 0 0 15px rgba(255, 0, 255, 0.5);
    margin-bottom: 15px;
    direction: rtl;
}

.text-tagline {
    font-size: 14px;
    color: #888;
    letter-spacing: 2px;
    text-transform: uppercase;
}

/* Scanlines */
.scanlines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 4px,
        rgba(0, 255, 65, 0.03) 4px,
        rgba(0, 255, 65, 0.03) 6px
    );
    pointer-events: none;
    z-index: 100;
}

/* Data Pulses */
.data-pulses {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.pulse {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #00ffff;
    border-radius: 50%;
    box-shadow: 0 0 10px #00ffff;
    animation: pulseMove 3s linear infinite;
}

.pulse-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.pulse-2 {
    top: 60%;
    right: 15%;
    animation-delay: 1s;
}

.pulse-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 2s;
}

/* Control Panel */
.control-panel {
    position: absolute;
    bottom: 30px;
    right: 30px;
    display: flex;
    gap: 15px;
    z-index: 200;
}

.control-btn {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid #00ff41;
    color: #00ff41;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(0, 255, 65, 0.1);
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
}

.control-btn span {
    margin-right: 5px;
}
