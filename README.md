# CGG - Code Guard Group | Animated Logo

## مجموعة حرس الكود - الشعار المتحرك

A futuristic, cyberpunk-style animated logo for Code Guard Group (CGG) featuring terminal interfaces, holographic effects, and digital shield animations.

## ✨ Features

### 🎬 Animation Sequence (8-10 seconds)
1. **Terminal Phase (0-3s)**: Ubuntu/Linux-style terminal with typing animation
2. **Glitch Effects (3-4s)**: Screen distortion and digital noise
3. **Code Streams (4-5s)**: Matrix-style code particles rushing to center
4. **Logo Formation (5-7s)**: Holographic CGG letters with neon glow
5. **Shield Creation (7-8s)**: 3D digital shield with flowing circuits
6. **Final Reveal (8-10s)**: Bilingual company text with pulse effects

### 🎨 Visual Effects
- **Cyberpunk Aesthetic**: Neon blue and purple color scheme
- **Holographic Glow**: Dynamic text shadows and gradients
- **3D Transformations**: Rotating shields and floating letters
- **Particle Systems**: Code streams and matrix background
- **Scanlines**: Retro CRT monitor effect
- **Glitch Effects**: Digital distortion and noise

### 🔊 Audio Features
- **Procedural Sound**: Web Audio API generated effects
- **Typing Sounds**: Realistic keyboard clicks
- **Glitch Audio**: Digital distortion effects
- **Shield Sounds**: Sci-fi activation tones
- **Sound Toggle**: Mute/unmute functionality

### 🎮 Interactive Controls
- **Replay Button**: Restart animation
- **Sound Toggle**: Enable/disable audio
- **Keyboard Shortcuts**:
  - `Space`: Replay animation
  - `M`: Toggle sound

## 🚀 Getting Started

### Prerequisites
- Modern web browser with HTML5 support
- Web Audio API support (for sound effects)

### Installation
1. Clone or download the project files
2. Open `index.html` in a web browser
3. The animation will start automatically

### File Structure
```
CGG/
├── index.html          # Main HTML structure
├── styles.css          # CSS animations and styling
├── script.js           # JavaScript animation logic
└── README.md           # Project documentation
```

## 🛠️ Technical Details

### Technologies Used
- **HTML5**: Semantic structure and canvas
- **CSS3**: Advanced animations and effects
- **JavaScript ES6+**: Animation control and audio
- **Web Audio API**: Procedural sound generation
- **Canvas API**: Particle effects and code streams

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### Performance Optimization
- Hardware-accelerated CSS transforms
- RequestAnimationFrame for smooth animations
- Efficient particle system management
- Responsive design for mobile devices

## 🎨 Customization

### Colors
The color scheme can be modified in `styles.css`:
```css
/* Primary colors */
--neon-blue: #00ffff;
--neon-purple: #ff00ff;
--matrix-green: #00ff41;
--background: #000000;
```

### Animation Timing
Adjust animation durations in `script.js`:
```javascript
// Phase durations (in milliseconds)
const TERMINAL_PHASE = 3000;
const GLITCH_PHASE = 1000;
const LOGO_PHASE = 2000;
const SHIELD_PHASE = 1500;
const FINAL_PHASE = 2000;
```

### Text Content
Modify company text in `index.html`:
```html
<div class="text-english">CODE GUARD GROUP</div>
<div class="text-arabic">مجموعة حرس الكود</div>
<div class="text-tagline">SECURING THE DIGITAL FUTURE</div>
```

## 📱 Responsive Design

The animation is fully responsive and adapts to different screen sizes:
- **Desktop**: Full 4K resolution support
- **Tablet**: Optimized layout and sizing
- **Mobile**: Touch-friendly controls and scaled elements

## 🔧 Advanced Features

### Particle System
- Dynamic code character generation
- Physics-based movement
- Opacity and size variations
- Color randomization

### Audio System
- Real-time sound synthesis
- Multiple oscillator types
- Frequency modulation
- Gain envelope control

### Performance Monitoring
- FPS optimization
- Memory management
- Efficient DOM manipulation
- CSS animation offloading

## 🐛 Troubleshooting

### Common Issues
1. **No Sound**: Check browser audio permissions
2. **Slow Performance**: Reduce particle count in script.js
3. **Mobile Issues**: Ensure touch events are enabled
4. **Font Loading**: Check internet connection for Google Fonts

### Debug Mode
Add `?debug=true` to URL for console logging:
```
file:///path/to/index.html?debug=true
```

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

## 📞 Contact

For questions or support regarding the CGG animated logo:
- Email: <EMAIL>
- Website: www.codeGuardGroup.com

---

**Code Guard Group** - مجموعة حرس الكود  
*Securing the Digital Future* - تأمين المستقبل الرقمي
