// ASCII Animations Manager
class ASCIIAnimations {
    constructor() {
        this.animations = [];
        this.asciiArt = {
            cgg: `
 ██████╗ ██████╗  ██████╗ 
██╔════╝██╔════╝ ██╔════╝ 
██║     ██║  ███╗██║  ███╗
██║     ██║   ██║██║   ██║
╚██████╗╚██████╔╝╚██████╔╝
 ╚═════╝ ╚═════╝  ╚═════╝ `,
            
            loading: `
    ████████████████████████████████
    ██                            ██
    ██  ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓  ██
    ██                            ██
    ████████████████████████████████`,
            
            security: `
    ╔══════════════════════════════╗
    ║          🔒 SECURE 🔒        ║
    ║                              ║
    ║  ████████████████████████    ║
    ║  ██                    ██    ║
    ║  ██  ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓  ██    ║
    ║  ██                    ██    ║
    ║  ████████████████████████    ║
    ╚══════════════════════════════╝`,
            
            hacker: `
    ░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░
    ░  ██   ██  █████   ██████  ░
    ░  ██   ██ ██   ██ ██       ░
    ░  ███████ ███████ ██       ░
    ░  ██   ██ ██   ██ ██       ░
    ░  ██   ██ ██   ██  ██████  ░
    ░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░`
        };
        
        this.init();
    }
    
    init() {
        this.setupAnimatedElements();
        this.startRandomAnimations();
        this.setupEventListeners();
    }
    
    setupAnimatedElements() {
        const asciiElements = document.querySelectorAll('.ascii-art');
        asciiElements.forEach(element => {
            this.addAnimation(element, 'glow');
        });
        
        const interactiveElements = document.querySelectorAll('.command-name, .terminal-prompt');
        interactiveElements.forEach(element => {
            element.classList.add('ascii-interactive');
        });
    }
    
    addAnimation(element, animationType, options = {}) {
        const animation = {
            element: element,
            type: animationType,
            options: options,
            isActive: false
        };
        
        this.animations.push(animation);
        this.applyAnimation(animation);
        
        return animation;
    }
    
    applyAnimation(animation) {
        const { element, type, options } = animation;
        
        this.removeAllAnimations(element);
        
        if (type === 'glitch') {
            element.setAttribute('data-text', element.textContent);
        }
        
        switch(type) {
            case 'glow':
                element.classList.add('ascii-animated');
                break;
            case 'typing':
                element.classList.add('ascii-typing');
                break;
            case 'glitch':
                element.classList.add('ascii-glitch');
                break;
            case 'pulse':
                element.classList.add('ascii-pulse');
                break;
            case 'rotate':
                element.classList.add('ascii-rotate');
                break;
            case 'bounce':
                element.classList.add('ascii-bounce');
                break;
            case 'wave':
                element.classList.add('ascii-wave');
                break;
            case 'fade-in':
                element.classList.add('ascii-fade-in');
                break;
            case 'slide-left':
                element.classList.add('ascii-slide-left');
                break;
            case 'slide-right':
                element.classList.add('ascii-slide-right');
                break;
            case 'zoom-in':
                element.classList.add('ascii-zoom-in');
                break;
            case 'flip':
                element.classList.add('ascii-flip');
                break;
            case 'shake':
                element.classList.add('ascii-shake');
                break;
            case 'color-cycle':
                element.classList.add('ascii-color-cycle');
                break;
            case 'breathe':
                element.classList.add('ascii-breathe');
                break;
            case 'matrix-rain':
                element.classList.add('ascii-matrix-rain');
                break;
            case 'loading':
                element.classList.add('ascii-loading');
                break;
        }
        
        animation.isActive = true;
        
        if (options.duration) {
            setTimeout(() => {
                this.removeAnimation(animation);
            }, options.duration);
        }
    }
    
    removeAnimation(animation) {
        this.removeAllAnimations(animation.element);
        animation.isActive = false;
    }
    
    removeAllAnimations(element) {
        const animationClasses = [
            'ascii-animated', 'ascii-typing', 'ascii-glitch', 'ascii-pulse',
            'ascii-rotate', 'ascii-bounce', 'ascii-wave', 'ascii-fade-in',
            'ascii-slide-left', 'ascii-slide-right', 'ascii-zoom-in',
            'ascii-flip', 'ascii-shake', 'ascii-color-cycle', 'ascii-breathe',
            'ascii-matrix-rain', 'ascii-loading'
        ];
        
        animationClasses.forEach(className => {
            element.classList.remove(className);
        });
    }
    
    createAnimatedASCII(artName, container, animationType = 'glow') {
        if (!this.asciiArt[artName]) return null;
        
        const asciiElement = document.createElement('div');
        asciiElement.className = 'ascii-animation';
        asciiElement.textContent = this.asciiArt[artName];
        
        if (container) {
            container.appendChild(asciiElement);
        }
        
        this.addAnimation(asciiElement, animationType);
        
        return asciiElement;
    }
    
    typewriteASCII(text, container, speed = 50) {
        const element = document.createElement('div');
        element.className = 'ascii-animation ascii-typing';
        container.appendChild(element);
        
        let i = 0;
        const typeInterval = setInterval(() => {
            element.textContent = text.substring(0, i);
            i++;
            
            if (i > text.length) {
                clearInterval(typeInterval);
                element.classList.remove('ascii-typing');
                element.classList.add('ascii-animated');
            }
        }, speed);
        
        return element;
    }
    
    morphASCII(element, newText, steps = 10) {
        const originalText = element.textContent;
        const maxLength = Math.max(originalText.length, newText.length);
        
        let currentStep = 0;
        const morphInterval = setInterval(() => {
            let morphedText = '';
            
            for (let i = 0; i < maxLength; i++) {
                const progress = currentStep / steps;
                const originalChar = originalText[i] || ' ';
                const newChar = newText[i] || ' ';
                
                if (Math.random() < progress) {
                    morphedText += newChar;
                } else {
                    const randomChars = '█▓▒░▄▀■□▪▫';
                    morphedText += Math.random() < 0.7 ? originalChar : 
                                  randomChars[Math.floor(Math.random() * randomChars.length)];
                }
            }
            
            element.textContent = morphedText;
            currentStep++;
            
            if (currentStep > steps) {
                clearInterval(morphInterval);
                element.textContent = newText;
            }
        }, 100);
    }
    
    startRandomAnimations() {
        setInterval(() => {
            this.triggerRandomAnimation();
        }, 8000);
    }
    
    triggerRandomAnimation() {
        const elements = document.querySelectorAll('.ascii-art, .terminal-welcome-text');
        if (elements.length === 0) return;
        
        const randomElement = elements[Math.floor(Math.random() * elements.length)];
        const animations = ['pulse', 'glow', 'breathe', 'color-cycle'];
        const randomAnimation = animations[Math.floor(Math.random() * animations.length)];
        
        this.addAnimation(randomElement, randomAnimation, { duration: 3000 });
    }
    
    setupEventListeners() {
        document.addEventListener('themeChanged', (e) => {
            this.updateAnimationsForTheme(e.detail.theme);
        });
        
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('ascii-interactive')) {
                this.addAnimation(e.target, 'pulse', { duration: 1000 });
            }
        });
        
        document.addEventListener('commandExecuted', () => {
            this.triggerCommandAnimation();
        });
        
        document.addEventListener('keydown', (e) => {
            if (e.key.length === 1) {
                this.triggerTypingAnimation();
            }
        });
    }
    
    updateAnimationsForTheme(theme) {
        const asciiElements = document.querySelectorAll('.ascii-animation');
        
        asciiElements.forEach(element => {
            switch(theme) {
                case 'cyberpunk':
                    element.style.animationDuration = '1s';
                    break;
                case 'hacker':
                    element.style.animationDuration = '0.5s';
                    break;
                case 'retro':
                    element.style.animationDuration = '4s';
                    break;
                default:
                    element.style.animationDuration = '3s';
            }
        });
    }
    
    triggerCommandAnimation() {
        const promptElements = document.querySelectorAll('.terminal-prompt');
        promptElements.forEach(element => {
            this.addAnimation(element, 'glow', { duration: 2000 });
        });
    }
    
    triggerTypingAnimation() {
        const cursorElements = document.querySelectorAll('.terminal-cursor');
        cursorElements.forEach(element => {
            this.addAnimation(element, 'pulse', { duration: 500 });
        });
    }
    
    createGlitchEffect(element, duration = 3000) {
        this.addAnimation(element, 'glitch', { duration: duration });
        
        const glitchInterval = setInterval(() => {
            element.classList.add('ascii-shake');
            setTimeout(() => {
                element.classList.remove('ascii-shake');
            }, 200);
        }, 500);
        
        setTimeout(() => {
            clearInterval(glitchInterval);
        }, duration);
    }
    
    // Public API methods
    animateElement(element, type, options) {
        return this.addAnimation(element, type, options);
    }
    
    stopAnimation(animation) {
        this.removeAnimation(animation);
    }
    
    createASCII(artName, container, animation) {
        return this.createAnimatedASCII(artName, container, animation);
    }
    
    typewrite(text, container, speed) {
        return this.typewriteASCII(text, container, speed);
    }
    
    morph(element, newText, steps) {
        return this.morphASCII(element, newText, steps);
    }
    
    glitch(element, duration) {
        return this.createGlitchEffect(element, duration);
    }
}

// Initialize ASCII animations
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.home-terminal')) {
        window.asciiAnimations = new ASCIIAnimations();
    }
});
