/* Terminal Themes System */
:root {
    /* Default Matrix Theme */
    --terminal-bg: #000000;
    --terminal-text: #00ff41;
    --terminal-accent: #00ff41;
    --terminal-secondary: #888888;
    --terminal-border: rgba(0, 255, 65, 0.3);
    --terminal-glow: rgba(0, 255, 65, 0.5);
    --matrix-char-color: #00ff41;
    --particle-color: #00ff41;
}

/* Matrix Theme (Default) */
.theme-matrix {
    --terminal-bg: #000000;
    --terminal-text: #00ff41;
    --terminal-accent: #00ff41;
    --terminal-secondary: #008f11;
    --terminal-border: rgba(0, 255, 65, 0.3);
    --terminal-glow: rgba(0, 255, 65, 0.5);
    --matrix-char-color: #00ff41;
    --particle-color: #00ff41;
}

/* Cyberpunk Theme */
.theme-cyberpunk {
    --terminal-bg: #0a0a0a;
    --terminal-text: #ff0080;
    --terminal-accent: #00ffff;
    --terminal-secondary: #ff6600;
    --terminal-border: rgba(255, 0, 128, 0.4);
    --terminal-glow: rgba(255, 0, 128, 0.6);
    --matrix-char-color: #ff0080;
    --particle-color: #00ffff;
}

/* Retro Theme */
.theme-retro {
    --terminal-bg: #1a1a00;
    --terminal-text: #ffaa00;
    --terminal-accent: #ff6600;
    --terminal-secondary: #cc8800;
    --terminal-border: rgba(255, 170, 0, 0.4);
    --terminal-glow: rgba(255, 170, 0, 0.6);
    --matrix-char-color: #ffaa00;
    --particle-color: #ff6600;
}

/* Hacker Theme */
.theme-hacker {
    --terminal-bg: #0d0d0d;
    --terminal-text: #ff3333;
    --terminal-accent: #ff0000;
    --terminal-secondary: #990000;
    --terminal-border: rgba(255, 51, 51, 0.4);
    --terminal-glow: rgba(255, 51, 51, 0.6);
    --matrix-char-color: #ff3333;
    --particle-color: #ff0000;
}

/* Apply theme colors to terminal elements */
.home-terminal {
    background: var(--terminal-bg);
    border-color: var(--terminal-border);
}

.home-terminal .terminal-body {
    background: var(--terminal-bg);
}

.ascii-art,
.terminal-welcome-text,
.command-name,
.terminal-prompt {
    color: var(--terminal-text);
    text-shadow: 0 0 8px var(--terminal-glow);
}

.terminal-subtitle,
.utilities-title,
.terminal-help {
    color: var(--terminal-secondary);
}

.command-description {
    color: var(--terminal-secondary);
}

.terminal-input-line {
    border-left-color: var(--terminal-accent);
}

.terminal-cursor {
    color: var(--terminal-accent);
    text-shadow: 0 0 10px var(--terminal-glow);
}

/* Matrix characters theme colors */
.matrix-char {
    color: var(--matrix-char-color);
}

/* Theme Selector */
.theme-selector {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    display: flex;
    gap: 10px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.theme-selector:hover {
    opacity: 1;
}

.theme-btn {
    width: 40px;
    height: 40px;
    border: 2px solid transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.theme-btn:hover {
    transform: scale(1.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.theme-btn.active {
    border-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.theme-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.8;
}

/* Theme button colors */
.theme-btn.matrix::before {
    background: linear-gradient(45deg, #000000 0%, #00ff41 50%, #008f11 100%);
}

.theme-btn.cyberpunk::before {
    background: linear-gradient(45deg, #0a0a0a 0%, #ff0080 50%, #00ffff 100%);
}

.theme-btn.retro::before {
    background: linear-gradient(45deg, #1a1a00 0%, #ffaa00 50%, #ff6600 100%);
}

.theme-btn.hacker::before {
    background: linear-gradient(45deg, #0d0d0d 0%, #ff3333 50%, #990000 100%);
}

/* Theme button icons */
.theme-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    filter: invert(1);
}

.theme-btn.matrix::after {
    content: 'M';
    font-family: 'Fira Code', monospace;
    font-weight: bold;
    font-size: 16px;
    color: #00ff41;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: none;
    filter: none;
}

.theme-btn.cyberpunk::after {
    content: 'C';
    font-family: 'Fira Code', monospace;
    font-weight: bold;
    font-size: 16px;
    color: #ff0080;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: none;
    filter: none;
}

.theme-btn.retro::after {
    content: 'R';
    font-family: 'Fira Code', monospace;
    font-weight: bold;
    font-size: 16px;
    color: #ffaa00;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: none;
    filter: none;
}

.theme-btn.hacker::after {
    content: 'H';
    font-family: 'Fira Code', monospace;
    font-weight: bold;
    font-size: 16px;
    color: #ff3333;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: none;
    filter: none;
}

/* Theme transition effects */
.home-terminal,
.home-terminal * {
    transition: color 0.5s ease, background-color 0.5s ease, border-color 0.5s ease, text-shadow 0.5s ease;
}

/* Responsive theme selector */
@media (max-width: 768px) {
    .theme-selector {
        top: 10px;
        left: 10px;
        gap: 8px;
    }
    
    .theme-btn {
        width: 35px;
        height: 35px;
    }
    
    .theme-btn.matrix::after,
    .theme-btn.cyberpunk::after,
    .theme-btn.retro::after,
    .theme-btn.hacker::after {
        font-size: 14px;
    }
}
