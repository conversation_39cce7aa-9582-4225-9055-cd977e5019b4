/* Advanced Neon Glow Effects */

/* Base neon glow */
.neon {
    color: var(--terminal-text);
    text-shadow: 
        0 0 5px var(--terminal-glow),
        0 0 10px var(--terminal-glow),
        0 0 15px var(--terminal-glow),
        0 0 20px var(--terminal-glow);
    animation: neon-flicker 2s infinite alternate;
}

/* Neon flicker animation */
@keyframes neon-flicker {
    0%, 18%, 22%, 25%, 53%, 57%, 100% {
        text-shadow: 
            0 0 5px var(--terminal-glow),
            0 0 10px var(--terminal-glow),
            0 0 15px var(--terminal-glow),
            0 0 20px var(--terminal-glow),
            0 0 35px var(--terminal-glow),
            0 0 40px var(--terminal-glow);
    }
    20%, 24%, 55% {
        text-shadow: 
            0 0 2px var(--terminal-glow),
            0 0 5px var(--terminal-glow),
            0 0 8px var(--terminal-glow),
            0 0 12px var(--terminal-glow);
    }
}

/* Intense neon glow */
.neon-intense {
    color: var(--terminal-text);
    text-shadow: 
        0 0 5px var(--terminal-glow),
        0 0 10px var(--terminal-glow),
        0 0 15px var(--terminal-glow),
        0 0 20px var(--terminal-glow),
        0 0 35px var(--terminal-glow),
        0 0 40px var(--terminal-glow),
        0 0 50px var(--terminal-glow),
        0 0 75px var(--terminal-glow);
    animation: neon-pulse 1.5s ease-in-out infinite alternate;
}

@keyframes neon-pulse {
    from {
        text-shadow: 
            0 0 5px var(--terminal-glow),
            0 0 10px var(--terminal-glow),
            0 0 15px var(--terminal-glow),
            0 0 20px var(--terminal-glow),
            0 0 35px var(--terminal-glow),
            0 0 40px var(--terminal-glow);
    }
    to {
        text-shadow: 
            0 0 2px var(--terminal-glow),
            0 0 5px var(--terminal-glow),
            0 0 8px var(--terminal-glow),
            0 0 12px var(--terminal-glow),
            0 0 25px var(--terminal-glow),
            0 0 30px var(--terminal-glow),
            0 0 40px var(--terminal-glow),
            0 0 50px var(--terminal-glow),
            0 0 75px var(--terminal-glow);
    }
}

/* Neon border effects */
.neon-border {
    border: 1px solid var(--terminal-accent);
    box-shadow: 
        0 0 5px var(--terminal-glow),
        0 0 10px var(--terminal-glow),
        inset 0 0 5px var(--terminal-glow),
        inset 0 0 10px var(--terminal-glow);
    animation: neon-border-pulse 2s ease-in-out infinite alternate;
}

@keyframes neon-border-pulse {
    from {
        box-shadow: 
            0 0 5px var(--terminal-glow),
            0 0 10px var(--terminal-glow),
            inset 0 0 5px var(--terminal-glow),
            inset 0 0 10px var(--terminal-glow);
    }
    to {
        box-shadow: 
            0 0 10px var(--terminal-glow),
            0 0 20px var(--terminal-glow),
            0 0 30px var(--terminal-glow),
            inset 0 0 10px var(--terminal-glow),
            inset 0 0 20px var(--terminal-glow);
    }
}

/* Neon underline effect */
.neon-underline {
    position: relative;
    text-decoration: none;
}

.neon-underline::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--terminal-accent);
    box-shadow: 
        0 0 5px var(--terminal-glow),
        0 0 10px var(--terminal-glow);
    animation: neon-underline-glow 1.5s ease-in-out infinite alternate;
}

@keyframes neon-underline-glow {
    from {
        box-shadow: 
            0 0 5px var(--terminal-glow),
            0 0 10px var(--terminal-glow);
        opacity: 0.8;
    }
    to {
        box-shadow: 
            0 0 10px var(--terminal-glow),
            0 0 20px var(--terminal-glow),
            0 0 30px var(--terminal-glow);
        opacity: 1;
    }
}

/* Neon background glow */
.neon-bg {
    background: rgba(0, 0, 0, 0.8);
    box-shadow: 
        0 0 20px var(--terminal-glow),
        inset 0 0 20px rgba(0, 0, 0, 0.5);
    border: 1px solid var(--terminal-accent);
    animation: neon-bg-pulse 3s ease-in-out infinite;
}

@keyframes neon-bg-pulse {
    0%, 100% {
        box-shadow: 
            0 0 20px var(--terminal-glow),
            inset 0 0 20px rgba(0, 0, 0, 0.5);
    }
    50% {
        box-shadow: 
            0 0 40px var(--terminal-glow),
            0 0 60px var(--terminal-glow),
            inset 0 0 30px rgba(0, 0, 0, 0.3);
    }
}

/* Neon loading effect */
.neon-loading {
    position: relative;
    overflow: hidden;
}

.neon-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        var(--terminal-glow),
        transparent
    );
    animation: neon-loading-sweep 2s infinite;
}

@keyframes neon-loading-sweep {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Neon text reveal effect */
.neon-reveal {
    position: relative;
    color: transparent;
    background: linear-gradient(
        90deg,
        transparent 0%,
        var(--terminal-text) 50%,
        transparent 100%
    );
    background-size: 200% 100%;
    background-clip: text;
    -webkit-background-clip: text;
    animation: neon-reveal-text 3s ease-in-out infinite;
}

@keyframes neon-reveal-text {
    0%, 100% {
        background-position: -200% 0;
    }
    50% {
        background-position: 200% 0;
    }
}

/* Neon circuit effect */
.neon-circuit {
    position: relative;
}

.neon-circuit::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(90deg, var(--terminal-accent) 1px, transparent 1px),
        linear-gradient(0deg, var(--terminal-accent) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.3;
    animation: neon-circuit-flow 4s linear infinite;
}

@keyframes neon-circuit-flow {
    0% {
        background-position: 0 0;
        opacity: 0.3;
    }
    50% {
        opacity: 0.6;
    }
    100% {
        background-position: 20px 20px;
        opacity: 0.3;
    }
}

/* Neon wave effect */
.neon-wave {
    position: relative;
    overflow: hidden;
}

.neon-wave::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        transparent 30%,
        var(--terminal-glow) 50%,
        transparent 70%
    );
    opacity: 0.5;
    animation: neon-wave-move 3s ease-in-out infinite;
    transform: translateX(-100%) translateY(-100%);
}

@keyframes neon-wave-move {
    0% {
        transform: translateX(-100%) translateY(-100%);
    }
    50% {
        transform: translateX(0%) translateY(0%);
    }
    100% {
        transform: translateX(100%) translateY(100%);
    }
}

/* Neon typing effect */
.neon-typing {
    position: relative;
    color: var(--terminal-text);
    text-shadow: 
        0 0 5px var(--terminal-glow),
        0 0 10px var(--terminal-glow);
}

.neon-typing::after {
    content: '|';
    color: var(--terminal-accent);
    text-shadow: 
        0 0 5px var(--terminal-glow),
        0 0 10px var(--terminal-glow),
        0 0 15px var(--terminal-glow);
    animation: neon-cursor-blink 1s infinite;
}

@keyframes neon-cursor-blink {
    0%, 50% {
        opacity: 1;
        text-shadow: 
            0 0 5px var(--terminal-glow),
            0 0 10px var(--terminal-glow),
            0 0 15px var(--terminal-glow);
    }
    51%, 100% {
        opacity: 0;
    }
}

/* Interactive neon effects */
.neon-interactive {
    transition: all 0.3s ease;
    cursor: pointer;
}

.neon-interactive:hover {
    color: var(--terminal-accent);
    text-shadow: 
        0 0 5px var(--terminal-glow),
        0 0 10px var(--terminal-glow),
        0 0 15px var(--terminal-glow),
        0 0 20px var(--terminal-glow),
        0 0 35px var(--terminal-glow),
        0 0 40px var(--terminal-glow);
    transform: scale(1.05);
}

/* Neon button effects */
.neon-button {
    background: transparent;
    border: 2px solid var(--terminal-accent);
    color: var(--terminal-text);
    padding: 10px 20px;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 
        0 0 10px var(--terminal-glow),
        inset 0 0 10px rgba(0, 0, 0, 0.5);
}

.neon-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s ease;
}

.neon-button:hover {
    color: var(--terminal-bg);
    background: var(--terminal-accent);
    box-shadow: 
        0 0 20px var(--terminal-glow),
        0 0 40px var(--terminal-glow),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
    text-shadow: none;
}

.neon-button:hover::before {
    left: 100%;
}

/* Theme-specific neon adjustments */
.theme-cyberpunk .neon {
    animation-duration: 1s;
}

.theme-retro .neon {
    animation-duration: 3s;
}

.theme-hacker .neon-intense {
    animation-duration: 0.8s;
}

/* Performance optimizations */
.neon,
.neon-intense,
.neon-border,
.neon-button {
    will-change: text-shadow, box-shadow;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .neon,
    .neon-intense,
    .neon-border,
    .neon-pulse,
    .neon-loading,
    .neon-reveal,
    .neon-circuit,
    .neon-wave,
    .neon-typing {
        animation: none;
    }
    
    .neon,
    .neon-intense {
        text-shadow: 
            0 0 5px var(--terminal-glow),
            0 0 10px var(--terminal-glow);
    }
}
