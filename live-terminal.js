// Live Interactive Terminal for Home Page
class LiveTerminal {
    constructor() {
        this.currentCommandIndex = 0;
        this.isTyping = false;
        this.typewriterSpeed = 50;
        this.commandDelay = 2000;
        this.outputLines = [];
        
        // Default commands and responses - can be customized by admin
        this.defaultCommands = [
            {
                command: "whoami",
                response: "CGG Security Group - Elite Cybersecurity Team",
                type: "info"
            },
            {
                command: "ls -la /security",
                response: [
                    "drwxr-xr-x  5 <USER> <GROUP>  4096 Dec 15 2024 threat-detection/",
                    "drwxr-xr-x  3 <USER> <GROUP>  4096 Dec 15 2024 encryption/",
                    "drwxr-xr-x  4 <USER> <GROUP>  4096 Dec 15 2024 network-security/",
                    "drwxr-xr-x  2 <USER> <GROUP>  4096 Dec 15 2024 identity-management/"
                ],
                type: "success"
            },
            {
                command: "cat /etc/cgg-motto",
                response: "🛡️ Securing the Digital Future, One Algorithm at a Time",
                type: "highlight"
            },
            {
                command: "ps aux | grep security",
                response: [
                    "root     1337  0.1  2.1 AI-ThreatDetector  running",
                    "root     2048  0.2  1.8 QuantumEncryption  active", 
                    "root     4096  0.1  1.5 NetworkShield     monitoring"
                ],
                type: "success"
            },
            {
                command: "uptime",
                response: "System uptime: 99.9% | Threats blocked: 1,337,420 | Clients protected: 50+",
                type: "info"
            },
            {
                command: "echo $MISSION",
                response: "Protecting digital assets with cutting-edge cybersecurity solutions",
                type: "highlight"
            }
        ];

        this.interactiveCommands = [
            { cmd: "scan", desc: "Security Scan", icon: "🔍" },
            { cmd: "encrypt", desc: "Encrypt Data", icon: "🔐" },
            { cmd: "firewall", desc: "Firewall Status", icon: "🛡️" },
            { cmd: "services", desc: "View Services", icon: "⚙️" },
            { cmd: "team", desc: "Meet Team", icon: "👥" },
            { cmd: "help", desc: "Show Help", icon: "❓" }
        ];

        this.init();
    }

    init() {
        this.loadCustomCommands();
        this.createInteractiveButtons();
        this.startCommandCycle();
        this.setupEventListeners();
    }

    loadCustomCommands() {
        // Load custom commands from admin if available
        if (window.adminManager && window.adminManager.data.homeTerminal) {
            this.commands = window.adminManager.data.homeTerminal.commands || this.defaultCommands;
            this.interactiveCommands = window.adminManager.data.homeTerminal.interactiveCommands || this.interactiveCommands;
        } else {
            this.commands = this.defaultCommands;
        }
    }

    createInteractiveButtons() {
        const buttonsContainer = document.getElementById('command-buttons');
        if (!buttonsContainer) return;

        buttonsContainer.innerHTML = '';
        
        this.interactiveCommands.forEach(cmd => {
            const button = document.createElement('button');
            button.className = 'interactive-cmd-btn';
            button.innerHTML = `
                <span class="cmd-icon">${cmd.icon}</span>
                <span class="cmd-text">${cmd.desc}</span>
                <span class="cmd-command">${cmd.cmd}</span>
            `;
            
            button.addEventListener('click', () => {
                this.executeInteractiveCommand(cmd.cmd);
                this.playCommandSound();
            });
            
            buttonsContainer.appendChild(button);
        });
    }

    startCommandCycle() {
        this.addBootSequence();
        setTimeout(() => {
            this.runNextCommand();
        }, 3000);
    }

    addBootSequence() {
        const bootMessages = [
            "🚀 CGG Security System Initializing...",
            "🔐 Loading encryption modules... OK",
            "🛡️ Activating threat detection... OK", 
            "🌐 Establishing secure connections... OK",
            "✅ System ready. Welcome to CGG Terminal!"
        ];

        bootMessages.forEach((msg, index) => {
            setTimeout(() => {
                this.addOutputLine(msg, 'boot');
                this.playBootSound();
            }, index * 800);
        });
    }

    runNextCommand() {
        if (this.isTyping) return;
        
        const command = this.commands[this.currentCommandIndex];
        this.typeCommand(command.command, () => {
            setTimeout(() => {
                this.showCommandResponse(command);
                setTimeout(() => {
                    this.currentCommandIndex = (this.currentCommandIndex + 1) % this.commands.length;
                    this.runNextCommand();
                }, this.commandDelay);
            }, 500);
        });
    }

    typeCommand(command, callback) {
        this.isTyping = true;
        const commandElement = document.getElementById('live-command');
        let currentChar = 0;
        
        commandElement.textContent = '';
        
        const typeInterval = setInterval(() => {
            if (currentChar < command.length) {
                commandElement.textContent += command[currentChar];
                currentChar++;
                this.playTypingSound();
            } else {
                clearInterval(typeInterval);
                this.isTyping = false;
                if (callback) callback();
            }
        }, this.typewriterSpeed);
    }

    showCommandResponse(command) {
        // Add command to output
        this.addOutputLine(`root@cgg-secure:~# ${command.command}`, 'command');
        
        // Add response
        if (Array.isArray(command.response)) {
            command.response.forEach(line => {
                this.addOutputLine(line, command.type);
            });
        } else {
            this.addOutputLine(command.response, command.type);
        }
        
        // Clear current command
        document.getElementById('live-command').textContent = '';
        
        // Limit output lines
        if (this.outputLines.length > 15) {
            this.outputLines = this.outputLines.slice(-15);
            this.updateTerminalContent();
        }
    }

    addOutputLine(text, type = 'normal') {
        this.outputLines.push({ text, type, timestamp: Date.now() });
        this.updateTerminalContent();
    }

    updateTerminalContent() {
        const content = document.getElementById('live-terminal-content');
        if (!content) return;
        
        content.innerHTML = this.outputLines.map(line => 
            `<div class="terminal-line ${line.type}">${line.text}</div>`
        ).join('');
        
        // Auto scroll to bottom
        content.scrollTop = content.scrollHeight;
    }

    executeInteractiveCommand(cmd) {
        // Add command to output
        this.addOutputLine(`root@cgg-secure:~# ${cmd}`, 'command');
        
        // Execute command through main terminal if available
        if (window.terminalManager) {
            const response = window.terminalManager.executeCommand(cmd);
            if (response) {
                this.addOutputLine(response, 'interactive');
            }
        } else {
            // Fallback responses
            this.addOutputLine(this.getCommandResponse(cmd), 'interactive');
        }
    }

    getCommandResponse(cmd) {
        const responses = {
            scan: "🔍 Security scan initiated... 0 threats detected ✅",
            encrypt: "🔐 Data encryption active - AES-256 enabled",
            firewall: "🛡️ Firewall status: ACTIVE | Blocked: 1,337 attempts",
            services: "⚙️ Redirecting to services page...",
            team: "👥 Loading team information...",
            help: "❓ Type 'help' in full terminal for complete command list"
        };
        
        return responses[cmd] || `Command '${cmd}' executed successfully`;
    }

    setupEventListeners() {
        // Click on terminal to focus
        const terminal = document.getElementById('live-terminal');
        if (terminal) {
            terminal.addEventListener('click', () => {
                this.focusTerminal();
            });
        }
    }

    focusTerminal() {
        const inputLine = document.getElementById('live-input-line');
        if (inputLine) {
            inputLine.classList.add('focused');
            setTimeout(() => {
                inputLine.classList.remove('focused');
            }, 2000);
        }
    }

    // Sound effects
    playTypingSound() {
        // Create subtle typing sound
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        gainNode.gain.setValueAtTime(0.01, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.1);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    }

    playCommandSound() {
        // Create command execution sound
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(1200, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(600, audioContext.currentTime + 0.2);
        gainNode.gain.setValueAtTime(0.02, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.2);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.2);
    }

    playBootSound() {
        // Create boot sound
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.3);
        gainNode.gain.setValueAtTime(0.015, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.3);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.3);
    }

    // Method to update commands from admin
    updateCommands(newCommands, newInteractiveCommands) {
        this.commands = newCommands || this.defaultCommands;
        this.interactiveCommands = newInteractiveCommands || this.interactiveCommands;
        this.createInteractiveButtons();
    }
}

// Initialize live terminal when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.liveTerminal = new LiveTerminal();
});
