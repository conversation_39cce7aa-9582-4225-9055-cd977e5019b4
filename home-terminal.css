/* Home Terminal Styles */

.home-terminal {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    background: #000000;
    border: none;
    border-radius: 0;
    box-shadow: none;
    backdrop-filter: none;
    font-family: 'Fira Code', 'Courier New', monospace;
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10;
    overflow: hidden;
}

.home-terminal .terminal-header {
    background: #000000;
    padding: 8px 15px;
    border-bottom: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 0;
    position: relative;
    min-height: 30px;
}

.home-terminal .terminal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(0, 255, 65, 0.1),
        transparent
    );
    animation: terminalHeaderScan 3s ease-in-out infinite;
}

.home-terminal .terminal-buttons {
    display: flex;
    gap: 10px;
    z-index: 1;
}

.home-terminal .btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    box-shadow: inset 0 1px 0 rgba(255,255,255,0.2);
}

.home-terminal .btn.close {
    background: #ff5f57;
    background: linear-gradient(135deg, #ff6b5a 0%, #ff5f57 50%, #e04b3d 100%);
}
.home-terminal .btn.minimize {
    background: #ffbd2e;
    background: linear-gradient(135deg, #ffca3a 0%, #ffbd2e 50%, #e6a82a 100%);
}
.home-terminal .btn.maximize {
    background: #28ca42;
    background: linear-gradient(135deg, #32d74b 0%, #28ca42 50%, #20a934 100%);
}

.home-terminal .terminal-title {
    color: #00ff41;
    font-size: 13px;
    font-weight: 500;
    text-shadow: none;
    z-index: 1;
    font-family: 'Fira Code', monospace;
    letter-spacing: 0.5px;
}

/* Remove terminal status - not needed for this design */

.home-terminal .terminal-body {
    flex: 1;
    padding: 20px 25px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #000000;
    font-family: 'Fira Code', monospace;
}

.terminal-content {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 15px;
    scrollbar-width: thin;
    scrollbar-color: #00ff41 transparent;
    min-height: 0;
    padding: 0;
}

/* ASCII Art Styles */
.ascii-art {
    color: #00ff41;
    font-family: 'Fira Code', monospace;
    font-size: 12px;
    line-height: 1.1;
    margin-bottom: 15px;
    white-space: pre;
    text-shadow: 0 0 8px rgba(0, 255, 65, 0.4);
    font-weight: 600;
    letter-spacing: 1px;
}

.terminal-welcome-text {
    color: #00ff41;
    font-size: 14px;
    margin-bottom: 10px;
    font-weight: 500;
}

.terminal-subtitle {
    color: #888;
    font-size: 12px;
    margin-bottom: 30px;
    font-style: italic;
}

/* Utilities Section */
.utilities-section {
    margin-top: 20px;
    margin-bottom: 30px;
}

.utilities-title {
    color: #888;
    font-size: 14px;
    margin-bottom: 15px;
    font-weight: 600;
}

.utility-command {
    display: flex;
    margin-bottom: 8px;
    font-family: 'Fira Code', monospace;
    font-size: 13px;
}

.command-name {
    color: #00ff41;
    width: 120px;
    font-weight: 600;
}

.command-separator {
    color: #666;
    margin: 0 10px;
}

.command-description {
    color: #ccc;
    flex: 1;
}

/* Terminal Help Text */
.terminal-help {
    color: #666;
    font-size: 12px;
    font-style: italic;
    margin-top: 20px;
    margin-bottom: 20px;
}

.terminal-content::-webkit-scrollbar {
    width: 6px;
}

.terminal-content::-webkit-scrollbar-track {
    background: transparent;
}

.terminal-content::-webkit-scrollbar-thumb {
    background: rgba(0, 255, 65, 0.3);
    border-radius: 3px;
}

.terminal-content::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 65, 0.5);
}

.terminal-line {
    margin-bottom: 6px;
    animation: fadeInUp 0.3s ease;
    line-height: 1.3;
    word-wrap: break-word;
}

.terminal-line.command {
    color: #00ff41;
    font-weight: 600;
}

.terminal-line.output {
    color: #8be9fd;
    margin-left: 20px;
}

.terminal-line.success {
    color: #50fa7b;
    margin-left: 20px;
}

.terminal-line.warning {
    color: #ffb86c;
    margin-left: 20px;
}

.terminal-line.error {
    color: #ff5555;
    margin-left: 20px;
}

.terminal-line.info {
    color: #bd93f9;
    margin-left: 20px;
}

.terminal-input-line {
    display: flex;
    align-items: center;
    background: transparent;
    border: none;
    border-radius: 0;
    padding: 8px 0;
    margin-top: auto;
    border-left: 3px solid #00ff41;
    padding-left: 10px;
}

.terminal-prompt {
    color: #00ff41;
    margin-right: 10px;
    font-weight: 600;
    text-shadow: 0 0 5px rgba(0, 255, 65, 0.5);
    white-space: nowrap;
}

.terminal-command {
    color: #fff;
    font-family: 'Fira Code', 'Courier New', monospace;
    font-size: 14px;
}

.terminal-cursor {
    color: #00ff41;
    animation: cursorBlink 1s infinite;
    margin-left: 2px;
    font-weight: bold;
}

/* Terminal footer removed for cleaner design */

/* Matrix Rain Background */
.matrix-rain {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
}

.matrix-char {
    position: absolute;
    color: #00ff41;
    font-family: 'Fira Code', monospace;
    font-size: 14px;
    animation: matrixFall linear infinite;
    opacity: 0.7;
}

/* Navigation Hint - Hidden for cleaner terminal look */
.navigation-hint {
    display: none;
}

.hint-text {
    color: #00ff41;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Terminal Settings Styles */
.terminal-settings {
    margin-bottom: 30px;
}

.settings-section {
    background: rgba(0, 255, 65, 0.05);
    border: 1px solid rgba(0, 255, 65, 0.2);
    border-radius: 10px;
    padding: 20px;
}

.settings-section h4 {
    color: #00ff41;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.setting-item label {
    color: #8be9fd;
    font-size: 14px;
    font-weight: 600;
}

.setting-item input[type="number"] {
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(0, 255, 65, 0.3);
    border-radius: 5px;
    padding: 8px 12px;
    color: #fff;
    font-family: 'Fira Code', monospace;
}

.setting-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: #00ff41;
}

.save-settings-btn {
    background: linear-gradient(45deg, #00ff41, #00cc33);
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    color: #000;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.save-settings-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 65, 0.3);
}

.terminal-texts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

/* Terminal Text Card */
.terminal-text-card {
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(0, 255, 65, 0.3);
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s ease;
}

.terminal-text-card:hover {
    border-color: rgba(0, 255, 65, 0.6);
    box-shadow: 0 5px 15px rgba(0, 255, 65, 0.2);
}

.terminal-text-card h4 {
    color: #00ff41;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.terminal-text-card .text-content {
    color: #8be9fd;
    font-family: 'Fira Code', monospace;
    background: rgba(0, 0, 0, 0.5);
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 15px;
    white-space: pre-wrap;
}

.terminal-text-card .text-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.text-type {
    background: rgba(0, 255, 65, 0.2);
    color: #00ff41;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.text-delay {
    color: #ffb86c;
    font-size: 12px;
}

.terminal-text-card .card-actions {
    display: flex;
    gap: 10px;
}

.edit-text-btn, .delete-text-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.edit-text-btn {
    background: rgba(0, 255, 255, 0.2);
    color: #00ffff;
    border: 1px solid rgba(0, 255, 255, 0.3);
}

.edit-text-btn:hover {
    background: rgba(0, 255, 255, 0.3);
}

.delete-text-btn {
    background: rgba(255, 85, 85, 0.2);
    color: #ff5555;
    border: 1px solid rgba(255, 85, 85, 0.3);
}

.delete-text-btn:hover {
    background: rgba(255, 85, 85, 0.3);
}

.add-output-btn {
    background: rgba(0, 255, 65, 0.2);
    color: #00ff41;
    border: 1px solid rgba(0, 255, 65, 0.3);
    border-radius: 5px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    margin-top: 10px;
}

.add-output-btn:hover {
    background: rgba(0, 255, 65, 0.3);
}

/* Animations */
@keyframes terminalHeaderScan {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
}

@keyframes cursorBlink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(3px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes matrixFall {
    0% {
        transform: translateY(-100vh);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh);
        opacity: 0;
    }
}

@keyframes hintPulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

@keyframes clickRipple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(3);
        opacity: 0;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .home-terminal {
        width: 100vw;
        height: 100vh;
    }

    .home-terminal .terminal-header {
        padding: 6px 12px;
        min-height: 30px;
    }

    .home-terminal .terminal-body {
        padding: 10px 15px;
    }

    .terminal-stats {
        flex-direction: column;
        gap: 10px;
    }

    .stat-item {
        flex-direction: row;
        gap: 8px;
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }

    .terminal-texts-grid {
        grid-template-columns: 1fr;
    }

    .navigation-hint {
        bottom: 8px;
        right: 8px;
        font-size: 10px;
        padding: 6px 10px;
    }

    .terminal-footer {
        padding: 8px 15px;
    }
}
