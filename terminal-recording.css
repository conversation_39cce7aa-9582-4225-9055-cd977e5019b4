/* Terminal Recording System */

/* Recording Controls */
.recording-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 8px;
    z-index: 100;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.recording-controls:hover {
    opacity: 1;
}

.recording-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.8);
    color: var(--terminal-text);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid var(--terminal-border);
}

.recording-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    border-color: var(--terminal-accent);
    box-shadow: 0 0 10px var(--terminal-glow);
}

.recording-btn.active {
    background: var(--terminal-accent);
    color: var(--terminal-bg);
    box-shadow: 0 0 15px var(--terminal-glow);
}

/* Recording indicator */
.recording-indicator {
    position: absolute;
    top: 5px;
    left: 5px;
    width: 12px;
    height: 12px;
    background: #ff0000;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 101;
}

.recording-indicator.active {
    opacity: 1;
    animation: recording-pulse 1s ease-in-out infinite;
}

@keyframes recording-pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
}

/* Recording status */
.recording-status {
    position: absolute;
    top: 50px;
    right: 10px;
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid var(--terminal-border);
    border-radius: 6px;
    padding: 8px 12px;
    color: var(--terminal-text);
    font-family: 'Fira Code', monospace;
    font-size: 11px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 100;
}

.recording-status.visible {
    opacity: 1;
    transform: translateY(0);
}

.recording-timer {
    color: var(--terminal-accent);
    font-weight: bold;
}

/* Playback Controls */
.playback-controls {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid var(--terminal-border);
    border-radius: 8px;
    padding: 8px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 100;
}

.playback-controls.visible {
    opacity: 1;
    visibility: visible;
}

.playback-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 6px;
    background: transparent;
    color: var(--terminal-text);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.playback-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--terminal-accent);
}

.playback-btn.active {
    background: var(--terminal-accent);
    color: var(--terminal-bg);
}

/* Progress bar */
.playback-progress {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 8px;
}

.progress-bar {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    position: relative;
    cursor: pointer;
}

.progress-fill {
    height: 100%;
    background: var(--terminal-accent);
    border-radius: 2px;
    width: 0%;
    transition: width 0.1s ease;
}

.progress-handle {
    position: absolute;
    top: -4px;
    width: 12px;
    height: 12px;
    background: var(--terminal-accent);
    border-radius: 50%;
    cursor: pointer;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.progress-bar:hover .progress-handle {
    opacity: 1;
}

.progress-time {
    font-family: 'Fira Code', monospace;
    font-size: 10px;
    color: var(--terminal-secondary);
    min-width: 80px;
    text-align: center;
}

/* Speed control */
.speed-control {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-left: 8px;
}

.speed-btn {
    padding: 4px 8px;
    border: 1px solid var(--terminal-border);
    border-radius: 4px;
    background: transparent;
    color: var(--terminal-text);
    font-family: 'Fira Code', monospace;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.speed-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.speed-btn.active {
    background: var(--terminal-accent);
    color: var(--terminal-bg);
}

/* Recording list */
.recording-list {
    position: absolute;
    top: 80px;
    right: 10px;
    width: 250px;
    max-height: 300px;
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid var(--terminal-border);
    border-radius: 8px;
    padding: 10px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 99;
    overflow-y: auto;
}

.recording-list.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.recording-list-header {
    color: var(--terminal-text);
    font-family: 'Fira Code', monospace;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--terminal-border);
}

.recording-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    margin-bottom: 5px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.recording-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left: 3px solid var(--terminal-accent);
}

.recording-info {
    flex: 1;
}

.recording-name {
    color: var(--terminal-text);
    font-family: 'Fira Code', monospace;
    font-size: 11px;
    font-weight: bold;
}

.recording-meta {
    color: var(--terminal-secondary);
    font-family: 'Fira Code', monospace;
    font-size: 9px;
    margin-top: 2px;
}

.recording-actions {
    display: flex;
    gap: 4px;
}

.recording-action-btn {
    width: 20px;
    height: 20px;
    border: none;
    border-radius: 3px;
    background: transparent;
    color: var(--terminal-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    transition: all 0.3s ease;
}

.recording-action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--terminal-text);
}

.recording-action-btn.delete:hover {
    background: rgba(255, 0, 0, 0.2);
    color: #ff4444;
}

/* Export dialog */
.export-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid var(--terminal-border);
    border-radius: 8px;
    padding: 20px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.export-dialog.visible {
    opacity: 1;
    visibility: visible;
}

.export-dialog-header {
    color: var(--terminal-text);
    font-family: 'Fira Code', monospace;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 15px;
    text-align: center;
}

.export-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.export-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border: 1px solid var(--terminal-border);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.export-option:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: var(--terminal-accent);
}

.export-option input[type="radio"] {
    accent-color: var(--terminal-accent);
}

.export-option label {
    color: var(--terminal-text);
    font-family: 'Fira Code', monospace;
    font-size: 12px;
    cursor: pointer;
    flex: 1;
}

.export-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.export-btn {
    padding: 8px 16px;
    border: 1px solid var(--terminal-border);
    border-radius: 4px;
    background: transparent;
    color: var(--terminal-text);
    font-family: 'Fira Code', monospace;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.export-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--terminal-accent);
}

.export-btn.primary {
    background: var(--terminal-accent);
    color: var(--terminal-bg);
}

.export-btn.primary:hover {
    background: var(--terminal-text);
}

/* Overlay */
.recording-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.recording-overlay.visible {
    opacity: 1;
    visibility: visible;
}

/* Responsive design */
@media (max-width: 768px) {
    .recording-controls {
        top: 5px;
        right: 5px;
        gap: 4px;
    }

    .recording-btn {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .playback-controls {
        bottom: 5px;
        padding: 6px;
        gap: 6px;
    }

    .playback-btn {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .progress-bar {
        width: 120px;
    }

    .recording-list {
        width: 200px;
        right: 5px;
    }

    .export-dialog {
        width: 90%;
        max-width: 350px;
    }
}

/* Click ripple effect */
@keyframes clickRipple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* Performance optimizations */
.recording-controls,
.playback-controls,
.recording-list {
    will-change: opacity, transform;
    transform: translateZ(0);
}
