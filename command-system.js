// Advanced Command System with History and Auto-completion
class CommandSystem {
    constructor() {
        this.history = [];
        this.historyIndex = -1;
        this.currentInput = '';
        this.suggestions = [];
        this.suggestionIndex = -1;
        
        // Available commands
        this.commands = {
            'help': {
                description: 'Show available commands',
                usage: 'help [command]',
                category: 'system'
            },
            'clear': {
                description: 'Clear terminal screen',
                usage: 'clear',
                category: 'system'
            },
            'history': {
                description: 'Show command history',
                usage: 'history [count]',
                category: 'system'
            },
            'echo': {
                description: 'Display text',
                usage: 'echo [text]',
                category: 'utility'
            },
            'date': {
                description: 'Show current date and time',
                usage: 'date',
                category: 'utility'
            },
            'whoami': {
                description: 'Show current user',
                usage: 'whoami',
                category: 'system'
            },
            'pwd': {
                description: 'Show current directory',
                usage: 'pwd',
                category: 'system'
            },
            'ls': {
                description: 'List directory contents',
                usage: 'ls [options]',
                category: 'system'
            },
            'cat': {
                description: 'Display file contents',
                usage: 'cat [file]',
                category: 'utility'
            },
            'grep': {
                description: 'Search text patterns',
                usage: 'grep [pattern] [file]',
                category: 'utility'
            },
            'ps': {
                description: 'Show running processes',
                usage: 'ps [options]',
                category: 'system'
            },
            'top': {
                description: 'Show system processes',
                usage: 'top',
                category: 'system'
            },
            'cgg': {
                description: 'CGG security tools',
                usage: 'cgg [command]',
                category: 'security'
            },
            'hack': {
                description: 'Ethical hacking tools',
                usage: 'hack [target]',
                category: 'security'
            },
            'encrypt': {
                description: 'Encrypt data',
                usage: 'encrypt [data]',
                category: 'security'
            },
            'decrypt': {
                description: 'Decrypt data',
                usage: 'decrypt [data]',
                category: 'security'
            },
            'scan': {
                description: 'Security scan',
                usage: 'scan [target]',
                category: 'security'
            },
            'theme': {
                description: 'Change terminal theme',
                usage: 'theme [matrix|cyberpunk|retro|hacker]',
                category: 'appearance'
            },
            'particles': {
                description: 'Control particle system',
                usage: 'particles [on|off|density]',
                category: 'appearance'
            },
            'sound': {
                description: 'Control sound settings',
                usage: 'sound [on|off]',
                category: 'settings'
            },
            'exit': {
                description: 'Exit terminal',
                usage: 'exit',
                category: 'system'
            }
        };
        
        this.init();
    }
    
    init() {
        this.loadHistory();
        this.setupEventListeners();
        this.createSuggestionBox();
    }
    
    loadHistory() {
        const saved = localStorage.getItem('terminalHistory');
        if (saved) {
            try {
                this.history = JSON.parse(saved);
            } catch (e) {
                console.log('Error loading history:', e);
                this.history = [];
            }
        }
    }
    
    saveHistory() {
        // Keep only last 100 commands
        if (this.history.length > 100) {
            this.history = this.history.slice(-100);
        }
        localStorage.setItem('terminalHistory', JSON.stringify(this.history));
    }
    
    setupEventListeners() {
        document.addEventListener('keydown', (e) => {
            this.handleKeyDown(e);
        });
        
        document.addEventListener('keyup', (e) => {
            this.handleKeyUp(e);
        });
    }
    
    createSuggestionBox() {
        if (document.querySelector('.suggestion-box')) return;
        
        const box = document.createElement('div');
        box.className = 'suggestion-box';
        box.style.cssText = `
            position: absolute;
            background: rgba(0, 0, 0, 0.95);
            border: 1px solid var(--terminal-accent);
            border-radius: 4px;
            padding: 5px 0;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            font-family: 'Fira Code', monospace;
            font-size: 12px;
            backdrop-filter: blur(10px);
        `;
        
        document.body.appendChild(box);
    }
    
    handleKeyDown(e) {
        const currentLine = document.getElementById('current-line');
        if (!currentLine || !currentLine.contains(e.target) && e.target !== document.body) return;
        
        switch(e.key) {
            case 'ArrowUp':
                e.preventDefault();
                this.navigateHistory('up');
                break;
                
            case 'ArrowDown':
                e.preventDefault();
                this.navigateHistory('down');
                break;
                
            case 'Tab':
                e.preventDefault();
                this.handleAutoComplete();
                break;
                
            case 'Enter':
                e.preventDefault();
                this.executeCommand();
                break;
                
            case 'Escape':
                this.hideSuggestions();
                break;
        }
    }
    
    handleKeyUp(e) {
        if (e.key === 'Tab' || e.key === 'Enter' || e.key === 'ArrowUp' || e.key === 'ArrowDown') return;
        
        const command = this.getCurrentCommand();
        if (command.length > 0) {
            this.showSuggestions(command);
        } else {
            this.hideSuggestions();
        }
    }
    
    getCurrentCommand() {
        const commandElement = document.getElementById('current-command');
        return commandElement ? commandElement.textContent.trim() : '';
    }
    
    setCurrentCommand(command) {
        const commandElement = document.getElementById('current-command');
        if (commandElement) {
            commandElement.textContent = command;
        }
    }
    
    navigateHistory(direction) {
        if (this.history.length === 0) return;
        
        if (direction === 'up') {
            if (this.historyIndex === -1) {
                this.currentInput = this.getCurrentCommand();
                this.historyIndex = this.history.length - 1;
            } else if (this.historyIndex > 0) {
                this.historyIndex--;
            }
        } else if (direction === 'down') {
            if (this.historyIndex === -1) return;
            
            if (this.historyIndex < this.history.length - 1) {
                this.historyIndex++;
            } else {
                this.historyIndex = -1;
                this.setCurrentCommand(this.currentInput);
                return;
            }
        }
        
        if (this.historyIndex >= 0 && this.historyIndex < this.history.length) {
            this.setCurrentCommand(this.history[this.historyIndex]);
        }
    }
    
    handleAutoComplete() {
        const input = this.getCurrentCommand();
        const parts = input.split(' ');
        const currentWord = parts[parts.length - 1];
        
        // Find matching commands
        const matches = Object.keys(this.commands).filter(cmd => 
            cmd.startsWith(currentWord.toLowerCase())
        );
        
        if (matches.length === 1) {
            // Single match - complete it
            parts[parts.length - 1] = matches[0];
            this.setCurrentCommand(parts.join(' '));
        } else if (matches.length > 1) {
            // Multiple matches - show suggestions
            this.showCommandSuggestions(matches);
        }
    }
    
    showSuggestions(input) {
        const matches = Object.keys(this.commands).filter(cmd => 
            cmd.toLowerCase().includes(input.toLowerCase())
        );
        
        if (matches.length > 0) {
            this.showCommandSuggestions(matches.slice(0, 8)); // Show max 8 suggestions
        } else {
            this.hideSuggestions();
        }
    }
    
    showCommandSuggestions(commands) {
        const box = document.querySelector('.suggestion-box');
        if (!box) return;
        
        box.innerHTML = '';
        
        commands.forEach((cmd, index) => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.style.cssText = `
                padding: 4px 12px;
                cursor: pointer;
                color: var(--terminal-text);
                border-left: 3px solid transparent;
                transition: all 0.2s ease;
            `;
            
            item.innerHTML = `
                <span style="color: var(--terminal-accent); font-weight: bold;">${cmd}</span>
                <span style="color: var(--terminal-secondary); margin-left: 10px; font-size: 10px;">
                    ${this.commands[cmd].description}
                </span>
            `;
            
            item.addEventListener('mouseenter', () => {
                item.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                item.style.borderLeftColor = 'var(--terminal-accent)';
            });
            
            item.addEventListener('mouseleave', () => {
                item.style.backgroundColor = 'transparent';
                item.style.borderLeftColor = 'transparent';
            });
            
            item.addEventListener('click', () => {
                this.setCurrentCommand(cmd);
                this.hideSuggestions();
            });
            
            box.appendChild(item);
        });
        
        // Position the suggestion box
        const currentLine = document.getElementById('current-line');
        if (currentLine) {
            const rect = currentLine.getBoundingClientRect();
            box.style.left = rect.left + 'px';
            box.style.top = (rect.bottom + 5) + 'px';
            box.style.display = 'block';
        }
    }
    
    hideSuggestions() {
        const box = document.querySelector('.suggestion-box');
        if (box) {
            box.style.display = 'none';
        }
    }
    
    executeCommand() {
        const command = this.getCurrentCommand().trim();
        if (!command) return;
        
        // Add to history
        if (this.history[this.history.length - 1] !== command) {
            this.history.push(command);
            this.saveHistory();
        }
        
        // Reset history navigation
        this.historyIndex = -1;
        this.currentInput = '';
        
        // Hide suggestions
        this.hideSuggestions();
        
        // Execute the command
        this.processCommand(command);
        
        // Clear current command
        this.setCurrentCommand('');
    }
    
    processCommand(command) {
        const parts = command.split(' ');
        const cmd = parts[0].toLowerCase();
        const args = parts.slice(1);
        
        // Add command to terminal output
        this.addToTerminalOutput(`guest@cgg:~$ ${command}`);
        
        // Process the command
        switch(cmd) {
            case 'help':
                this.showHelp(args[0]);
                break;
            case 'clear':
                this.clearTerminal();
                break;
            case 'history':
                this.showHistory(args[0]);
                break;
            case 'theme':
                this.changeTheme(args[0]);
                break;
            case 'particles':
                this.controlParticles(args[0]);
                break;
            default:
                if (this.commands[cmd]) {
                    this.addToTerminalOutput(`Executing ${cmd}...`);
                } else {
                    this.addToTerminalOutput(`Command not found: ${cmd}`);
                    this.addToTerminalOutput(`Type 'help' for available commands.`);
                }
        }
    }
    
    addToTerminalOutput(text) {
        if (window.homeTerminal && window.homeTerminal.addToHistory) {
            window.homeTerminal.addToHistory('output', text);
        }
    }
    
    showHelp(specificCommand) {
        if (specificCommand && this.commands[specificCommand]) {
            const cmd = this.commands[specificCommand];
            this.addToTerminalOutput(`${specificCommand}: ${cmd.description}`);
            this.addToTerminalOutput(`Usage: ${cmd.usage}`);
            this.addToTerminalOutput(`Category: ${cmd.category}`);
        } else {
            this.addToTerminalOutput('Available commands:');
            Object.entries(this.commands).forEach(([name, info]) => {
                this.addToTerminalOutput(`  ${name.padEnd(12)} - ${info.description}`);
            });
            this.addToTerminalOutput('\nUse "help [command]" for detailed information.');
        }
    }
    
    clearTerminal() {
        const content = document.getElementById('home-terminal-content');
        if (content) {
            // Keep only the static content
            const staticContent = content.querySelector('.ascii-art')?.parentNode;
            if (staticContent) {
                const lines = content.querySelectorAll('.terminal-line');
                lines.forEach(line => line.remove());
            }
        }
    }
    
    showHistory(count) {
        const num = parseInt(count) || this.history.length;
        const recent = this.history.slice(-num);
        
        this.addToTerminalOutput('Command history:');
        recent.forEach((cmd, index) => {
            this.addToTerminalOutput(`  ${(this.history.length - recent.length + index + 1).toString().padStart(3)}: ${cmd}`);
        });
    }
    
    changeTheme(theme) {
        if (window.terminalThemes && theme) {
            window.terminalThemes.setTheme(theme);
            this.addToTerminalOutput(`Theme changed to: ${theme}`);
        } else {
            this.addToTerminalOutput('Available themes: matrix, cyberpunk, retro, hacker');
        }
    }
    
    controlParticles(action) {
        if (window.particleSystem) {
            switch(action) {
                case 'on':
                    window.particleSystem.resume();
                    this.addToTerminalOutput('Particle system enabled');
                    break;
                case 'off':
                    window.particleSystem.pause();
                    this.addToTerminalOutput('Particle system disabled');
                    break;
                case 'low':
                case 'medium':
                case 'high':
                    window.particleSystem.setDensity(action);
                    this.addToTerminalOutput(`Particle density set to: ${action}`);
                    break;
                default:
                    this.addToTerminalOutput('Usage: particles [on|off|low|medium|high]');
            }
        } else {
            this.addToTerminalOutput('Particle system not available');
        }
    }
}

// Initialize command system
document.addEventListener('DOMContentLoaded', () => {
    window.commandSystem = new CommandSystem();
});
