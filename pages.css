/* Pages Styles for CGG Website */

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #000000, #001122);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 1s ease-out;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
    color: #00ff41;
}

.loading-logo {
    font-size: 4rem;
    font-weight: 900;
    font-family: 'Orbitron', monospace;
    margin-bottom: 20px;
    text-shadow: 0 0 20px #00ff41;
    animation: loadingPulse 2s ease-in-out infinite;
}

.loading-text {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.8;
}

.loading-bar {
    width: 300px;
    height: 4px;
    background: rgba(0, 255, 65, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #00ff41, #00ffff);
    width: 0%;
    animation: loadingProgress 3s ease-out forwards;
}

/* Navigation */
.main-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 255, 65, 0.3);
    z-index: 1000;
    opacity: 0;
    transform: translateY(-100%);
    transition: all 0.5s ease;
}

.main-nav.visible {
    opacity: 1;
    transform: translateY(0);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-logo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.logo-text {
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    font-weight: 900;
    color: #00ff41;
    text-shadow: 0 0 10px #00ff41;
}

.logo-subtitle {
    font-size: 0.7rem;
    color: #888;
    margin-top: -5px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
    align-items: center;
}

.nav-link {
    color: #ccc;
    text-decoration: none;
    font-family: 'Orbitron', monospace;
    font-size: 0.9rem;
    padding: 10px 15px;
    border-radius: 5px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-link:hover,
.nav-link.active {
    color: #00ff41;
    background: rgba(0, 255, 65, 0.1);
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
}

.nav-link i {
    font-size: 0.8rem;
}

.terminal-toggle {
    background: rgba(0, 255, 255, 0.1) !important;
    color: #00ffff !important;
    border: 1px solid rgba(0, 255, 255, 0.3);
}

.terminal-toggle:hover {
    background: rgba(0, 255, 255, 0.2) !important;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5) !important;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: #00ff41;
    transition: all 0.3s ease;
}

/* Page Content */
.page-content {
    display: none;
    min-height: 100vh;
    padding: 100px 20px 50px;
    max-width: 1200px;
    margin: 0 auto;
    overflow-y: auto;
    position: relative;
}

.page-content.active {
    display: block;
}

/* Scrollbar Styling */
.page-content::-webkit-scrollbar {
    width: 8px;
}

.page-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
}

.page-content::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #00ff41, #00ffff);
    border-radius: 4px;
}

.page-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #00ffff, #ff00ff);
}

.page-header {
    text-align: center;
    margin-bottom: 60px;
}

.page-title {
    font-family: 'Orbitron', monospace;
    font-size: 3rem;
    font-weight: 900;
    color: #00ff41;
    text-shadow: 0 0 20px rgba(0, 255, 65, 0.5);
    margin-bottom: 20px;
}

.page-subtitle {
    font-size: 1.2rem;
    color: #888;
    max-width: 600px;
    margin: 0 auto;
}

/* Home Page Specific */
.home-content {
    opacity: 0;
    text-align: center;
    padding: 50px 0;
    animation: homeContentAppear 1s ease-out 10s forwards;
}

.hero-section {
    margin-bottom: 80px;
}

.hero-title {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 20px;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    color: #888;
    margin-bottom: 40px;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-btn {
    padding: 15px 30px;
    font-family: 'Orbitron', monospace;
    font-size: 1rem;
    font-weight: 600;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-btn.primary {
    background: linear-gradient(45deg, #00ff41, #00ffff);
    color: #000;
}

.cta-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 255, 65, 0.4);
}

.cta-btn.secondary {
    background: transparent;
    color: #00ffff;
    border: 2px solid #00ffff;
}

.cta-btn.secondary:hover {
    background: rgba(0, 255, 255, 0.1);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.stat-card {
    background: rgba(0, 255, 65, 0.05);
    border: 1px solid rgba(0, 255, 65, 0.2);
    border-radius: 10px;
    padding: 30px 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 255, 65, 0.2);
    border-color: rgba(0, 255, 65, 0.5);
}

.stat-number {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 900;
    color: #00ff41;
    text-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
    margin-bottom: 10px;
}

.stat-label {
    color: #888;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Services Page */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.service-card {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(0, 255, 65, 0.2);
    border-radius: 15px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 65, 0.1), transparent);
    transition: left 0.5s ease;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 255, 65, 0.5);
    box-shadow: 0 20px 40px rgba(0, 255, 65, 0.2);
}

.service-icon {
    font-size: 3rem;
    color: #00ff41;
    margin-bottom: 20px;
    text-shadow: 0 0 20px rgba(0, 255, 65, 0.5);
}

.service-card h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    color: #fff;
    margin-bottom: 15px;
}

.service-card p {
    color: #888;
    margin-bottom: 20px;
    line-height: 1.6;
}

.service-card ul {
    list-style: none;
    text-align: left;
}

.service-card li {
    color: #ccc;
    margin-bottom: 8px;
    position: relative;
    padding-left: 20px;
}

.service-card li::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: #00ff41;
    font-size: 0.8rem;
}

/* Projects Page */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 35px;
    max-width: 1400px;
    margin: 0 auto;
}

.project-card {
    background: rgba(0, 0, 0, 0.85);
    border: 2px solid rgba(0, 255, 65, 0.2);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    position: relative;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
    z-index: 1;
}

.project-card:hover::before {
    left: 100%;
}

.project-card:hover {
    transform: translateY(-15px) scale(1.02);
    border-color: rgba(0, 255, 255, 0.5);
    box-shadow:
        0 25px 50px rgba(0, 255, 255, 0.3),
        0 0 30px rgba(0, 255, 255, 0.2);
}

.project-image {
    height: 220px;
    background: linear-gradient(135deg, #001122, #002244, #001133);
    background-size: 200% 200%;
    animation: projectImageGlow 6s ease-in-out infinite;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.project-image::before {
    content: '< CODE />';
    font-family: 'Fira Code', monospace;
    font-size: 2.2rem;
    color: rgba(0, 255, 65, 0.4);
    font-weight: bold;
    text-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
    animation: codeFloat 3s ease-in-out infinite;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-btn {
    background: linear-gradient(45deg, #00ff41, #00ffff);
    color: #000;
    border: none;
    padding: 12px 25px;
    border-radius: 5px;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.project-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 255, 65, 0.4);
}

.project-content {
    padding: 35px 30px;
    position: relative;
    z-index: 2;
}

.project-content h3 {
    font-family: 'Orbitron', monospace;
    color: #fff;
    margin-bottom: 18px;
    font-size: 1.4rem;
    font-weight: 700;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.project-content p {
    color: #ccc;
    line-height: 1.7;
    margin-bottom: 25px;
    font-size: 0.95rem;
    text-align: justify;
}

.project-tags {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.tag {
    background: rgba(0, 255, 65, 0.1);
    color: #00ff41;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    border: 1px solid rgba(0, 255, 65, 0.3);
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tag:hover {
    background: rgba(0, 255, 65, 0.2);
    border-color: rgba(0, 255, 65, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 65, 0.3);
}

/* Team Page */
.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    max-width: 1400px;
    margin: 0 auto;
}

.team-card {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(0, 255, 65, 0.2);
    border-radius: 20px;
    padding: 35px 25px;
    text-align: center;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.team-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.team-card:hover::before {
    left: 100%;
}

.team-card:hover {
    transform: translateY(-15px) scale(1.02);
    border-color: rgba(0, 255, 255, 0.6);
    box-shadow:
        0 25px 50px rgba(0, 255, 255, 0.3),
        0 0 30px rgba(0, 255, 255, 0.2);
}

.team-avatar {
    width: 140px;
    height: 140px;
    margin: 0 auto 25px;
    border-radius: 50%;
    background: linear-gradient(135deg, #00ff41, #00ffff, #ff00ff);
    background-size: 200% 200%;
    animation: avatarGlow 4s ease-in-out infinite;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.4);
    overflow: hidden;
}

.avatar-placeholder {
    width: 130px;
    height: 130px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3.5rem;
    color: #00ff41;
    text-shadow: 0 0 20px rgba(0, 255, 65, 0.8);
    border: 2px solid rgba(0, 255, 65, 0.3);
}

.team-card h3 {
    font-family: 'Orbitron', monospace;
    color: #fff;
    margin-bottom: 12px;
    font-size: 1.4rem;
    font-weight: 700;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.team-role {
    color: #00ffff;
    font-weight: 700;
    margin-bottom: 18px;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: 0.95rem;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.team-bio {
    color: #ccc;
    line-height: 1.7;
    margin-bottom: 25px;
    font-size: 0.95rem;
    text-align: justify;
}

.team-social {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.team-social a {
    color: #888;
    font-size: 1.4rem;
    transition: all 0.3s ease;
    padding: 10px;
    border-radius: 50%;
    background: rgba(0, 255, 65, 0.05);
    border: 1px solid rgba(0, 255, 65, 0.2);
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.team-social a:hover {
    color: #00ff41;
    transform: translateY(-3px) scale(1.1);
    background: rgba(0, 255, 65, 0.1);
    border-color: rgba(0, 255, 65, 0.5);
    box-shadow: 0 5px 15px rgba(0, 255, 65, 0.3);
}

/* Blog Page */
.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
}

.blog-card {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(0, 255, 65, 0.2);
    border-radius: 15px;
    padding: 30px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.blog-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 0, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.blog-card:hover::before {
    left: 100%;
}

.blog-card:hover {
    transform: translateY(-10px);
    border-color: rgba(255, 0, 255, 0.5);
    box-shadow: 0 20px 40px rgba(255, 0, 255, 0.2);
}

.blog-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.blog-date {
    color: #888;
    font-size: 0.9rem;
}

.blog-category {
    background: rgba(255, 0, 255, 0.1);
    color: #ff00ff;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    border: 1px solid rgba(255, 0, 255, 0.3);
}

.blog-card h3 {
    font-family: 'Orbitron', monospace;
    color: #fff;
    margin-bottom: 15px;
    font-size: 1.3rem;
    line-height: 1.4;
}

.blog-card p {
    color: #888;
    line-height: 1.6;
    margin-bottom: 20px;
}

.blog-link {
    color: #ff00ff;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.blog-link:hover {
    color: #fff;
    transform: translateX(5px);
}

/* Contact Page */
.contact-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.contact-item i {
    font-size: 1.5rem;
    color: #00ff41;
    margin-top: 5px;
}

.contact-item h4 {
    color: #fff;
    margin-bottom: 8px;
    font-family: 'Orbitron', monospace;
}

.contact-item p {
    color: #888;
    line-height: 1.6;
}

.contact-form {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(0, 255, 65, 0.2);
    border-radius: 15px;
    padding: 40px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    background: rgba(0, 255, 65, 0.05);
    border: 1px solid rgba(0, 255, 65, 0.2);
    border-radius: 8px;
    padding: 15px;
    color: #fff;
    font-family: 'Rajdhani', sans-serif;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #00ff41;
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
    background: rgba(0, 255, 65, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #666;
}

.submit-btn {
    width: 100%;
    background: linear-gradient(45deg, #00ff41, #00ffff);
    color: #000;
    border: none;
    padding: 15px;
    border-radius: 8px;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 255, 65, 0.4);
}

/* Animations */
@keyframes loadingPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes loadingProgress {
    0% { width: 0%; }
    100% { width: 100%; }
}

@keyframes homeContentAppear {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(0, 0, 0, 0.98);
        flex-direction: column;
        justify-content: flex-start;
        padding-top: 50px;
        transition: left 0.3s ease;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .page-title {
        font-size: 2rem;
    }

    .hero-title {
        font-size: 1.8rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-btn {
        width: 100%;
        max-width: 300px;
    }

    .contact-container {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .services-grid,
    .projects-grid,
    .team-grid,
    .blog-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .team-card,
    .project-card {
        margin: 0 10px;
    }

    .team-avatar {
        width: 120px;
        height: 120px;
    }

    .avatar-placeholder {
        width: 110px;
        height: 110px;
        font-size: 3rem;
    }

    .project-image {
        height: 180px;
    }

    .project-image::before {
        font-size: 1.8rem;
    }
}

/* Login Page Styles */
.login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 80vh;
    padding: 20px;
}

.login-form-container {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #00ff41;
    border-radius: 20px;
    padding: 40px;
    max-width: 450px;
    width: 100%;
    box-shadow:
        0 0 50px rgba(0, 255, 65, 0.3),
        inset 0 0 50px rgba(0, 255, 65, 0.05);
    position: relative;
    overflow: hidden;
}

.login-form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 65, 0.1), transparent);
    animation: loginScan 3s ease-in-out infinite;
}

.login-header {
    text-align: center;
    margin-bottom: 40px;
}

.login-logo i {
    font-size: 4rem;
    color: #00ff41;
    text-shadow: 0 0 20px rgba(0, 255, 65, 0.5);
    margin-bottom: 20px;
    display: block;
}

.login-logo h2 {
    font-family: 'Orbitron', monospace;
    color: #fff;
    margin-bottom: 10px;
    font-size: 2rem;
}

.login-logo p {
    color: #888;
    font-size: 1rem;
}

.login-form .form-group {
    position: relative;
    margin-bottom: 25px;
}

.login-form label {
    display: block;
    color: #00ff41;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 0.9rem;
}

.login-form input {
    width: 100%;
    background: rgba(0, 255, 65, 0.05);
    border: 2px solid rgba(0, 255, 65, 0.3);
    border-radius: 10px;
    padding: 15px 50px 15px 15px;
    color: #fff;
    font-family: 'Rajdhani', sans-serif;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.login-form input:focus {
    outline: none;
    border-color: #00ff41;
    box-shadow: 0 0 20px rgba(0, 255, 65, 0.4);
    background: rgba(0, 255, 65, 0.1);
}

.input-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #00ff41;
    font-size: 1.2rem;
    margin-top: 12px;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #ccc;
}

.checkbox-container input {
    width: auto;
    margin-right: 10px;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: rgba(0, 255, 65, 0.1);
    border: 2px solid rgba(0, 255, 65, 0.3);
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input:checked + .checkmark {
    background: #00ff41;
    border-color: #00ff41;
}

.checkbox-container input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #000;
    font-weight: bold;
}

.login-btn {
    width: 100%;
    background: linear-gradient(45deg, #00ff41, #00ffff);
    color: #000;
    border: none;
    padding: 15px;
    border-radius: 10px;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 20px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 255, 65, 0.4);
}

.login-btn i {
    margin-right: 10px;
}

.login-footer {
    margin-top: 30px;
    text-align: center;
}

.security-notice {
    color: #888;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.security-notice i {
    color: #00ff41;
}

/* Dashboard Styles */
.dashboard-header {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(0, 255, 65, 0.2);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 40px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.admin-welcome {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 20px;
}

.admin-info {
    flex: 1;
}

.admin-info .page-title {
    margin-bottom: 10px;
    font-size: 2.5rem;
}

.admin-info .page-subtitle {
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.admin-name {
    color: #00ff41;
    font-weight: 700;
    text-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
}

.admin-status {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: #888;
}

.status-indicator {
    width: 12px;
    height: 12px;
    background: #00ff41;
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
    animation: statusPulse 2s ease-in-out infinite;
}

.status-text {
    color: #00ff41;
    font-weight: 600;
}

.admin-avatar {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #00ff41, #00ffff);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: #000;
    box-shadow: 0 0 30px rgba(0, 255, 65, 0.4);
    animation: adminAvatarGlow 3s ease-in-out infinite;
}

.admin-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.logout-btn {
    background: linear-gradient(135deg, rgba(255, 85, 85, 0.1), rgba(255, 85, 85, 0.2));
    border: 2px solid rgba(255, 85, 85, 0.3);
    color: #ff5555;
    padding: 15px 25px;
    border-radius: 12px;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.logout-btn:hover {
    background: linear-gradient(135deg, rgba(255, 85, 85, 0.2), rgba(255, 85, 85, 0.3));
    border-color: rgba(255, 85, 85, 0.6);
    box-shadow: 0 0 20px rgba(255, 85, 85, 0.4);
    transform: translateY(-2px);
}

.logout-btn i {
    font-size: 1.2rem;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.admin-stat {
    background: rgba(0, 255, 65, 0.05);
    border: 2px solid rgba(0, 255, 65, 0.2);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.admin-stat:hover {
    border-color: rgba(0, 255, 65, 0.5);
    box-shadow: 0 10px 30px rgba(0, 255, 65, 0.2);
}

.stat-icon {
    font-size: 2.5rem;
    color: #00ff41;
    text-shadow: 0 0 15px rgba(0, 255, 65, 0.5);
}

.stat-info {
    flex: 1;
}

.dashboard-tabs {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(0, 255, 65, 0.2);
    border-radius: 15px;
    overflow: hidden;
}

.tab-buttons {
    display: flex;
    background: rgba(0, 255, 65, 0.05);
    border-bottom: 2px solid rgba(0, 255, 65, 0.2);
    overflow-x: auto;
}

.tab-btn {
    background: transparent;
    border: none;
    color: #888;
    padding: 20px 25px;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    border-right: 1px solid rgba(0, 255, 65, 0.1);
}

.tab-btn:last-child {
    border-right: none;
}

.tab-btn.active,
.tab-btn:hover {
    background: rgba(0, 255, 65, 0.1);
    color: #00ff41;
}

.tab-btn i {
    margin-right: 8px;
}

.tab-content {
    display: none;
    padding: 30px;
}

.tab-content.active {
    display: block;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.content-header h3 {
    color: #fff;
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
}

.add-btn {
    background: linear-gradient(45deg, #00ff41, #00ffff);
    color: #000;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 65, 0.4);
}

.add-btn i {
    margin-right: 8px;
}

.admin-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.admin-item {
    background: rgba(0, 255, 65, 0.05);
    border: 2px solid rgba(0, 255, 65, 0.2);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
}

.admin-item:hover {
    border-color: rgba(0, 255, 65, 0.5);
    box-shadow: 0 5px 20px rgba(0, 255, 65, 0.2);
}

.admin-item h4 {
    color: #fff;
    margin-bottom: 10px;
    font-family: 'Orbitron', monospace;
}

.admin-item p {
    color: #888;
    margin-bottom: 15px;
    line-height: 1.5;
}

.admin-item-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.edit-btn,
.delete-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 5px;
    font-family: 'Orbitron', monospace;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.edit-btn {
    background: rgba(0, 255, 255, 0.1);
    color: #00ffff;
    border: 1px solid rgba(0, 255, 255, 0.3);
}

.edit-btn:hover {
    background: rgba(0, 255, 255, 0.2);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.4);
}

.delete-btn {
    background: rgba(255, 85, 85, 0.1);
    color: #ff5555;
    border: 1px solid rgba(255, 85, 85, 0.3);
}

.delete-btn:hover {
    background: rgba(255, 85, 85, 0.2);
    box-shadow: 0 0 10px rgba(255, 85, 85, 0.4);
}

/* Modal Styles */
.edit-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 3000;
    backdrop-filter: blur(10px);
}

.edit-modal.active {
    display: flex;
}

.modal-content {
    background: rgba(0, 0, 0, 0.95);
    border: 2px solid #00ff41;
    border-radius: 15px;
    padding: 30px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 0 50px rgba(0, 255, 65, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 255, 65, 0.2);
}

.modal-header h3 {
    color: #fff;
    font-family: 'Orbitron', monospace;
    margin: 0;
}

.modal-close {
    background: transparent;
    border: none;
    color: #ff5555;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modal-close:hover {
    color: #fff;
    transform: scale(1.1);
}

.modal-form .form-group {
    margin-bottom: 20px;
}

.modal-form label {
    display: block;
    color: #00ff41;
    margin-bottom: 8px;
    font-weight: 600;
}

.modal-form input,
.modal-form textarea,
.modal-form select {
    width: 100%;
    background: rgba(0, 255, 65, 0.05);
    border: 2px solid rgba(0, 255, 65, 0.2);
    border-radius: 8px;
    padding: 12px;
    color: #fff;
    font-family: 'Rajdhani', sans-serif;
    transition: all 0.3s ease;
}

.modal-form input:focus,
.modal-form textarea:focus,
.modal-form select:focus {
    outline: none;
    border-color: #00ff41;
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
}

.modal-form textarea {
    resize: vertical;
    min-height: 100px;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 25px;
    padding-top: 15px;
    border-top: 1px solid rgba(0, 255, 65, 0.2);
}

.cancel-btn {
    background: transparent;
    border: 2px solid #888;
    color: #888;
    padding: 10px 20px;
    border-radius: 8px;
    font-family: 'Orbitron', monospace;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cancel-btn:hover {
    border-color: #fff;
    color: #fff;
}

.save-btn {
    background: linear-gradient(45deg, #00ff41, #00ffff);
    color: #000;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 65, 0.4);
}

/* Animations */
@keyframes loginScan {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes avatarGlow {
    0%, 100% {
        background-position: 0% 50%;
        transform: scale(1);
    }
    25% {
        background-position: 100% 50%;
        transform: scale(1.05);
    }
    50% {
        background-position: 0% 50%;
        transform: scale(1);
    }
    75% {
        background-position: 100% 50%;
        transform: scale(1.05);
    }
}

@keyframes projectImageGlow {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes codeFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.4;
    }
    50% {
        transform: translateY(-10px) scale(1.1);
        opacity: 0.6;
    }
}

@keyframes statusPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.2);
    }
}

@keyframes adminAvatarGlow {
    0%, 100% {
        box-shadow: 0 0 30px rgba(0, 255, 65, 0.4);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 40px rgba(0, 255, 255, 0.6);
        transform: scale(1.05);
    }
}

/* Admin Item Styles */
.admin-item-image img {
    border: 2px solid rgba(0, 255, 65, 0.2);
    transition: all 0.3s ease;
}

.admin-item-image img:hover {
    border-color: rgba(0, 255, 65, 0.5);
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
}

.team-social-admin {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
}

.social-link {
    color: #888;
    font-size: 1.2rem;
    padding: 8px;
    border-radius: 50%;
    background: rgba(0, 255, 65, 0.05);
    border: 1px solid rgba(0, 255, 65, 0.2);
    transition: all 0.3s ease;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.social-link:hover {
    color: #00ff41;
    background: rgba(0, 255, 65, 0.1);
    border-color: rgba(0, 255, 65, 0.5);
    transform: translateY(-2px);
}

.social-link.linkedin:hover {
    color: #0077b5;
    border-color: rgba(0, 119, 181, 0.5);
    background: rgba(0, 119, 181, 0.1);
}

.social-link.twitter:hover {
    color: #1da1f2;
    border-color: rgba(29, 161, 242, 0.5);
    background: rgba(29, 161, 242, 0.1);
}

.social-link.github:hover {
    color: #333;
    border-color: rgba(51, 51, 51, 0.5);
    background: rgba(255, 255, 255, 0.1);
}

.project-link-btn,
.blog-link-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background: rgba(0, 255, 65, 0.1);
    color: #00ff41;
    text-decoration: none;
    border-radius: 8px;
    border: 1px solid rgba(0, 255, 65, 0.3);
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.project-link-btn:hover,
.blog-link-btn:hover {
    background: rgba(0, 255, 65, 0.2);
    border-color: rgba(0, 255, 65, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 65, 0.3);
}

.project-link-btn.demo {
    background: rgba(0, 255, 255, 0.1);
    color: #00ffff;
    border-color: rgba(0, 255, 255, 0.3);
}

.project-link-btn.demo:hover {
    background: rgba(0, 255, 255, 0.2);
    border-color: rgba(0, 255, 255, 0.6);
    box-shadow: 0 5px 15px rgba(0, 255, 255, 0.3);
}

.project-link-btn.download {
    background: rgba(255, 0, 255, 0.1);
    color: #ff00ff;
    border-color: rgba(255, 0, 255, 0.3);
}

.project-link-btn.download:hover {
    background: rgba(255, 0, 255, 0.2);
    border-color: rgba(255, 0, 255, 0.6);
    box-shadow: 0 5px 15px rgba(255, 0, 255, 0.3);
}

/* Public Page Enhancements */
.avatar-image {
    width: 130px;
    height: 130px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(0, 255, 65, 0.3);
    transition: all 0.3s ease;
}

.avatar-image:hover {
    border-color: rgba(0, 255, 65, 0.6);
    box-shadow: 0 0 20px rgba(0, 255, 65, 0.4);
}

.project-btn {
    background: linear-gradient(45deg, #00ff41, #00ffff);
    color: #000;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.project-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 255, 65, 0.4);
}

.project-btn i {
    font-size: 1rem;
}

.status-badge {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 10px;
}

.status-completed {
    background: rgba(80, 250, 123, 0.2);
    color: #50fa7b;
    border: 1px solid rgba(80, 250, 123, 0.4);
}

.status-in-progress {
    background: rgba(255, 184, 108, 0.2);
    color: #ffb86c;
    border: 1px solid rgba(255, 184, 108, 0.4);
}

.status-on-hold {
    background: rgba(255, 85, 85, 0.2);
    color: #ff5555;
    border: 1px solid rgba(255, 85, 85, 0.4);
}

.blog-image {
    border: 2px solid rgba(0, 255, 65, 0.2);
    transition: all 0.3s ease;
}

.blog-image:hover {
    border-color: rgba(0, 255, 65, 0.5);
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
}

.blog-author {
    color: #888;
    font-size: 0.9rem;
    margin: 15px 0;
    font-style: italic;
}

.blog-author::before {
    content: "✍️ ";
    margin-right: 5px;
}

/* Enhanced Project Image Overlay */
.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(5px);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

/* Team Social Links Enhancement */
.team-social a {
    transition: all 0.3s ease;
}

.team-social a[href*="linkedin"]:hover {
    color: #0077b5;
    background: rgba(0, 119, 181, 0.1);
    border-color: rgba(0, 119, 181, 0.5);
}

.team-social a[href*="twitter"]:hover {
    color: #1da1f2;
    background: rgba(29, 161, 242, 0.1);
    border-color: rgba(29, 161, 242, 0.5);
}

.team-social a[href*="github"]:hover {
    color: #333;
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}
