// Terminal Themes Manager
class TerminalThemes {
    constructor() {
        this.currentTheme = 'matrix';
        this.themes = {
            matrix: {
                name: 'Matrix',
                description: 'Classic green hacker terminal',
                colors: {
                    bg: '#000000',
                    text: '#00ff41',
                    accent: '#00ff41',
                    secondary: '#008f11'
                }
            },
            cyberpunk: {
                name: 'Cyberpunk',
                description: 'Neon pink and cyan futuristic',
                colors: {
                    bg: '#0a0a0a',
                    text: '#ff0080',
                    accent: '#00ffff',
                    secondary: '#ff6600'
                }
            },
            retro: {
                name: 'Retro',
                description: 'Warm orange vintage computer',
                colors: {
                    bg: '#1a1a00',
                    text: '#ffaa00',
                    accent: '#ff6600',
                    secondary: '#cc8800'
                }
            },
            hacker: {
                name: 'Hacker',
                description: 'Dangerous red warning theme',
                colors: {
                    bg: '#0d0d0d',
                    text: '#ff3333',
                    accent: '#ff0000',
                    secondary: '#990000'
                }
            }
        };
        
        this.init();
    }
    
    init() {
        this.createThemeSelector();
        this.loadSavedTheme();
        this.setupEventListeners();
    }
    
    createThemeSelector() {
        // Check if theme selector already exists
        if (document.querySelector('.theme-selector')) return;
        
        const selector = document.createElement('div');
        selector.className = 'theme-selector';
        selector.innerHTML = `
            <div class="theme-btn matrix active" data-theme="matrix" title="Matrix Theme"></div>
            <div class="theme-btn cyberpunk" data-theme="cyberpunk" title="Cyberpunk Theme"></div>
            <div class="theme-btn retro" data-theme="retro" title="Retro Theme"></div>
            <div class="theme-btn hacker" data-theme="hacker" title="Hacker Theme"></div>
        `;
        
        document.body.appendChild(selector);
    }
    
    setupEventListeners() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('theme-btn')) {
                const theme = e.target.dataset.theme;
                this.setTheme(theme);
            }
        });
        
        // Keyboard shortcuts for themes
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey) {
                switch(e.key) {
                    case 'M':
                        e.preventDefault();
                        this.setTheme('matrix');
                        break;
                    case 'C':
                        e.preventDefault();
                        this.setTheme('cyberpunk');
                        break;
                    case 'R':
                        e.preventDefault();
                        this.setTheme('retro');
                        break;
                    case 'H':
                        e.preventDefault();
                        this.setTheme('hacker');
                        break;
                }
            }
        });
    }
    
    setTheme(themeName) {
        if (!this.themes[themeName]) return;
        
        // Remove current theme class
        document.body.classList.remove(`theme-${this.currentTheme}`);
        
        // Add new theme class
        document.body.classList.add(`theme-${themeName}`);
        
        // Update current theme
        this.currentTheme = themeName;
        
        // Update active button
        document.querySelectorAll('.theme-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-theme="${themeName}"]`).classList.add('active');
        
        // Save theme preference
        localStorage.setItem('terminalTheme', themeName);
        
        // Update matrix rain colors
        this.updateMatrixRain();
        
        // Update particle system colors
        this.updateParticleSystem();
        
        // Play theme change sound
        this.playThemeChangeSound();
        
        // Show theme notification
        this.showThemeNotification(themeName);
        
        // Trigger theme change event
        document.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: themeName, colors: this.themes[themeName].colors }
        }));
    }
    
    loadSavedTheme() {
        const savedTheme = localStorage.getItem('terminalTheme');
        if (savedTheme && this.themes[savedTheme]) {
            this.setTheme(savedTheme);
        } else {
            this.setTheme('matrix'); // Default theme
        }
    }
    
    updateMatrixRain() {
        const matrixChars = document.querySelectorAll('.matrix-char');
        const theme = this.themes[this.currentTheme];
        
        matrixChars.forEach(char => {
            char.style.color = theme.colors.text;
            if (char.textContent === 'C' || char.textContent === 'G') {
                char.style.color = theme.colors.accent;
                char.style.textShadow = `0 0 10px ${theme.colors.accent}`;
            }
        });
    }
    
    updateParticleSystem() {
        // This will be implemented when we add the particle system
        if (window.particleSystem) {
            window.particleSystem.updateColors(this.themes[this.currentTheme].colors);
        }
    }
    
    playThemeChangeSound() {
        if (!window.homeTerminal || !window.homeTerminal.settings.soundEnabled) return;
        
        try {
            const audioContext = window.homeTerminal.audioContext;
            if (!audioContext) return;
            
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            // Theme change sound - ascending notes
            const frequencies = [400, 500, 600, 800];
            let currentFreq = 0;
            
            const playNote = () => {
                if (currentFreq < frequencies.length) {
                    oscillator.frequency.setValueAtTime(frequencies[currentFreq], audioContext.currentTime);
                    currentFreq++;
                    setTimeout(playNote, 100);
                }
            };
            
            oscillator.type = 'sine';
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.5);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.5);
            
            playNote();
        } catch (e) {
            console.log('Theme sound error:', e);
        }
    }
    
    showThemeNotification(themeName) {
        // Remove existing notification
        const existing = document.querySelector('.theme-notification');
        if (existing) existing.remove();
        
        const notification = document.createElement('div');
        notification.className = 'theme-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-icon">🎨</span>
                <span class="notification-text">Theme changed to ${this.themes[themeName].name}</span>
            </div>
        `;
        
        // Add notification styles
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid var(--terminal-accent);
            border-radius: 8px;
            padding: 10px 15px;
            color: var(--terminal-text);
            font-family: 'Fira Code', monospace;
            font-size: 12px;
            z-index: 1002;
            opacity: 0;
            transform: translateX(-20px);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);
        
        // Animate out and remove
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(-20px)';
            setTimeout(() => notification.remove(), 300);
        }, 2000);
    }
    
    getCurrentTheme() {
        return this.currentTheme;
    }
    
    getThemeColors(themeName = this.currentTheme) {
        return this.themes[themeName]?.colors || this.themes.matrix.colors;
    }
    
    // Method to cycle through themes
    nextTheme() {
        const themeNames = Object.keys(this.themes);
        const currentIndex = themeNames.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themeNames.length;
        this.setTheme(themeNames[nextIndex]);
    }
    
    // Method to get random theme
    randomTheme() {
        const themeNames = Object.keys(this.themes);
        const randomIndex = Math.floor(Math.random() * themeNames.length);
        this.setTheme(themeNames[randomIndex]);
    }
}

// Initialize themes when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.terminalThemes = new TerminalThemes();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TerminalThemes;
}
