// Terminal Recording System
class TerminalRecording {
    constructor() {
        this.isRecording = false;
        this.isPlaying = false;
        this.recordings = [];
        this.currentRecording = null;
        this.recordingData = [];
        this.startTime = null;
        this.playbackPosition = 0;
        this.playbackSpeed = 1;
        this.playbackInterval = null;

        this.init();
    }

    init() {
        // Temporarily disabled to avoid conflicts
        // this.createRecordingUI();
        // this.loadSavedRecordings();
        // this.setupEventListeners();
        console.log('Terminal Recording system initialized (disabled for now)');
    }

    createRecordingUI() {
        const terminal = document.querySelector('.home-terminal');
        if (!terminal) return;

        // Recording controls
        const recordingControls = document.createElement('div');
        recordingControls.className = 'recording-controls';
        recordingControls.innerHTML = `
            <button class="recording-btn" id="record-btn" title="Record (Ctrl+R)">⏺</button>
            <button class="recording-btn" id="stop-btn" title="Stop Recording">⏹</button>
            <button class="recording-btn" id="play-btn" title="Play Recording">▶</button>
            <button class="recording-btn" id="list-btn" title="Recording List">📋</button>
        `;

        // Recording indicator
        const recordingIndicator = document.createElement('div');
        recordingIndicator.className = 'recording-indicator';
        recordingIndicator.id = 'recording-indicator';

        // Recording status
        const recordingStatus = document.createElement('div');
        recordingStatus.className = 'recording-status';
        recordingStatus.id = 'recording-status';
        recordingStatus.innerHTML = `
            <div>Recording: <span class="recording-timer">00:00</span></div>
            <div>Events: <span id="event-count">0</span></div>
        `;

        // Playback controls
        const playbackControls = document.createElement('div');
        playbackControls.className = 'playback-controls';
        playbackControls.id = 'playback-controls';
        playbackControls.innerHTML = `
            <button class="playback-btn" id="play-pause-btn">▶</button>
            <button class="playback-btn" id="stop-playback-btn">⏹</button>
            <button class="playback-btn" id="restart-btn">⏮</button>
            <div class="playback-progress">
                <div class="progress-bar" id="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                    <div class="progress-handle" id="progress-handle"></div>
                </div>
                <div class="progress-time" id="progress-time">00:00 / 00:00</div>
            </div>
            <div class="speed-control">
                <button class="speed-btn" data-speed="0.5">0.5x</button>
                <button class="speed-btn active" data-speed="1">1x</button>
                <button class="speed-btn" data-speed="1.5">1.5x</button>
                <button class="speed-btn" data-speed="2">2x</button>
            </div>
        `;

        // Recording list
        const recordingList = document.createElement('div');
        recordingList.className = 'recording-list';
        recordingList.id = 'recording-list';
        recordingList.innerHTML = `
            <div class="recording-list-header">Saved Recordings</div>
            <div class="recording-items" id="recording-items"></div>
        `;

        // Export dialog
        const exportDialog = document.createElement('div');
        exportDialog.className = 'export-dialog';
        exportDialog.id = 'export-dialog';
        exportDialog.innerHTML = `
            <div class="export-dialog-header">Export Recording</div>
            <div class="export-options">
                <div class="export-option">
                    <input type="radio" name="export-format" value="json" id="export-json" checked>
                    <label for="export-json">JSON Format (for replay)</label>
                </div>
                <div class="export-option">
                    <input type="radio" name="export-format" value="text" id="export-text">
                    <label for="export-text">Text Format (readable)</label>
                </div>
                <div class="export-option">
                    <input type="radio" name="export-format" value="gif" id="export-gif">
                    <label for="export-gif">Animated GIF (visual)</label>
                </div>
            </div>
            <div class="export-actions">
                <button class="export-btn" id="export-cancel">Cancel</button>
                <button class="export-btn primary" id="export-confirm">Export</button>
            </div>
        `;

        // Overlay
        const overlay = document.createElement('div');
        overlay.className = 'recording-overlay';
        overlay.id = 'recording-overlay';

        // Add to DOM
        terminal.appendChild(recordingControls);
        terminal.appendChild(recordingIndicator);
        terminal.appendChild(recordingStatus);
        terminal.appendChild(playbackControls);
        terminal.appendChild(recordingList);
        document.body.appendChild(exportDialog);
        document.body.appendChild(overlay);
    }

    setupEventListeners() {
        // Recording controls
        document.getElementById('record-btn').addEventListener('click', () => {
            this.startRecording();
        });

        document.getElementById('stop-btn').addEventListener('click', () => {
            this.stopRecording();
        });

        document.getElementById('play-btn').addEventListener('click', () => {
            this.showRecordingList();
        });

        document.getElementById('list-btn').addEventListener('click', () => {
            this.toggleRecordingList();
        });

        // Playback controls
        document.getElementById('play-pause-btn').addEventListener('click', () => {
            this.togglePlayback();
        });

        document.getElementById('stop-playback-btn').addEventListener('click', () => {
            this.stopPlayback();
        });

        document.getElementById('restart-btn').addEventListener('click', () => {
            this.restartPlayback();
        });

        // Speed controls
        document.querySelectorAll('.speed-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.setPlaybackSpeed(parseFloat(btn.dataset.speed));
            });
        });

        // Progress bar
        document.getElementById('progress-bar').addEventListener('click', (e) => {
            this.seekPlayback(e);
        });

        // Export dialog
        document.getElementById('export-cancel').addEventListener('click', () => {
            this.hideExportDialog();
        });

        document.getElementById('export-confirm').addEventListener('click', () => {
            this.exportRecording();
        });

        // Overlay
        document.getElementById('recording-overlay').addEventListener('click', () => {
            this.hideExportDialog();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey) {
                switch(e.key) {
                    case 'r':
                        e.preventDefault();
                        this.toggleRecording();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.togglePlayback();
                        break;
                }
            }
        });

        // Listen for terminal events to record
        this.setupTerminalEventListeners();
    }

    setupTerminalEventListeners() {
        // Record keystrokes
        document.addEventListener('keydown', (e) => {
            if (this.isRecording) {
                this.recordEvent('keydown', {
                    key: e.key,
                    code: e.code,
                    ctrlKey: e.ctrlKey,
                    shiftKey: e.shiftKey,
                    altKey: e.altKey
                });
            }
        });

        // Record command execution
        document.addEventListener('commandExecuted', (e) => {
            if (this.isRecording) {
                this.recordEvent('command', {
                    command: e.detail.command,
                    output: e.detail.output
                });
            }
        });

        // Record theme changes
        document.addEventListener('themeChanged', (e) => {
            if (this.isRecording) {
                this.recordEvent('theme', {
                    theme: e.detail.theme
                });
            }
        });

        // Record clicks
        document.addEventListener('click', (e) => {
            if (this.isRecording && e.target.closest('.home-terminal')) {
                this.recordEvent('click', {
                    x: e.clientX,
                    y: e.clientY,
                    target: e.target.className
                });
            }
        });
    }

    startRecording() {
        if (this.isRecording) return;

        this.isRecording = true;
        this.recordingData = [];
        this.startTime = Date.now();

        // Update UI
        document.getElementById('record-btn').classList.add('active');
        document.getElementById('recording-indicator').classList.add('active');
        document.getElementById('recording-status').classList.add('visible');

        // Start timer
        this.updateRecordingTimer();

        // Record initial state
        this.recordEvent('start', {
            timestamp: this.startTime,
            theme: window.terminalThemes?.getCurrentTheme() || 'matrix',
            terminalContent: this.getTerminalContent()
        });
    }

    stopRecording() {
        if (!this.isRecording) return;

        this.isRecording = false;

        // Update UI
        document.getElementById('record-btn').classList.remove('active');
        document.getElementById('recording-indicator').classList.remove('active');
        document.getElementById('recording-status').classList.remove('visible');

        // Save recording
        this.saveRecording();
    }

    toggleRecording() {
        if (this.isRecording) {
            this.stopRecording();
        } else {
            this.startRecording();
        }
    }

    recordEvent(type, data) {
        if (!this.isRecording) return;

        const event = {
            type: type,
            timestamp: Date.now() - this.startTime,
            data: data
        };

        this.recordingData.push(event);

        // Update event count
        document.getElementById('event-count').textContent = this.recordingData.length;
    }

    updateRecordingTimer() {
        if (!this.isRecording) return;

        const elapsed = Date.now() - this.startTime;
        const minutes = Math.floor(elapsed / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);

        document.querySelector('.recording-timer').textContent =
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        setTimeout(() => this.updateRecordingTimer(), 1000);
    }

    saveRecording() {
        const recording = {
            id: Date.now(),
            name: `Recording ${new Date().toLocaleString()}`,
            duration: this.recordingData.length > 0 ?
                     this.recordingData[this.recordingData.length - 1].timestamp : 0,
            events: this.recordingData,
            created: new Date().toISOString()
        };

        this.recordings.push(recording);
        this.saveRecordingsToStorage();
        this.updateRecordingList();
    }

    playRecording(recording) {
        if (this.isPlaying) return;

        this.currentRecording = recording;
        this.playbackPosition = 0;
        this.isPlaying = true;

        // Show playback controls
        document.getElementById('playback-controls').classList.add('visible');
        document.getElementById('play-pause-btn').textContent = '⏸';

        // Start playback
        this.playNextEvent();
    }

    playNextEvent() {
        if (!this.isPlaying || !this.currentRecording) return;

        const events = this.currentRecording.events;
        if (this.playbackPosition >= events.length) {
            this.stopPlayback();
            return;
        }

        const event = events[this.playbackPosition];
        this.executeEvent(event);

        // Update progress
        this.updatePlaybackProgress();

        // Schedule next event
        const nextEventIndex = this.playbackPosition + 1;
        if (nextEventIndex < events.length) {
            const nextEvent = events[nextEventIndex];
            const delay = (nextEvent.timestamp - event.timestamp) / this.playbackSpeed;

            this.playbackInterval = setTimeout(() => {
                this.playbackPosition = nextEventIndex;
                this.playNextEvent();
            }, delay);
        } else {
            this.stopPlayback();
        }
    }

    executeEvent(event) {
        switch(event.type) {
            case 'start':
                // Set initial state
                if (event.data.theme && window.terminalThemes) {
                    window.terminalThemes.setTheme(event.data.theme);
                }
                break;

            case 'keydown':
                // Simulate keypress
                this.simulateKeypress(event.data);
                break;

            case 'command':
                // Execute command
                this.simulateCommand(event.data);
                break;

            case 'theme':
                // Change theme
                if (window.terminalThemes) {
                    window.terminalThemes.setTheme(event.data.theme);
                }
                break;

            case 'click':
                // Simulate click
                this.simulateClick(event.data);
                break;
        }
    }

    simulateKeypress(data) {
        // Add character to current command
        const commandElement = document.getElementById('current-command');
        if (commandElement && data.key.length === 1) {
            commandElement.textContent += data.key;
        }
    }

    simulateCommand(data) {
        // Add command to terminal output
        if (window.homeTerminal && window.homeTerminal.addToHistory) {
            window.homeTerminal.addToHistory('command', data.command);
            if (data.output) {
                window.homeTerminal.addToHistory('output', data.output);
            }
        }
    }

    simulateClick(data) {
        // Create visual click effect
        const effect = document.createElement('div');
        effect.style.cssText = `
            position: fixed;
            left: ${data.x}px;
            top: ${data.y}px;
            width: 20px;
            height: 20px;
            border: 2px solid var(--terminal-accent);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            animation: clickRipple 0.6s ease-out forwards;
        `;

        document.body.appendChild(effect);
        setTimeout(() => effect.remove(), 600);
    }

    togglePlayback() {
        if (this.isPlaying) {
            this.pausePlayback();
        } else {
            this.resumePlayback();
        }
    }

    pausePlayback() {
        this.isPlaying = false;
        if (this.playbackInterval) {
            clearTimeout(this.playbackInterval);
        }
        document.getElementById('play-pause-btn').textContent = '▶';
    }

    resumePlayback() {
        this.isPlaying = true;
        document.getElementById('play-pause-btn').textContent = '⏸';
        this.playNextEvent();
    }

    stopPlayback() {
        this.isPlaying = false;
        this.playbackPosition = 0;

        if (this.playbackInterval) {
            clearTimeout(this.playbackInterval);
        }

        // Hide playback controls
        document.getElementById('playback-controls').classList.remove('visible');
        document.getElementById('play-pause-btn').textContent = '▶';
    }

    restartPlayback() {
        this.stopPlayback();
        if (this.currentRecording) {
            this.playRecording(this.currentRecording);
        }
    }

    setPlaybackSpeed(speed) {
        this.playbackSpeed = speed;

        // Update UI
        document.querySelectorAll('.speed-btn').forEach(btn => {
            btn.classList.toggle('active', parseFloat(btn.dataset.speed) === speed);
        });
    }

    updatePlaybackProgress() {
        if (!this.currentRecording) return;

        const progress = this.playbackPosition / this.currentRecording.events.length;
        const progressFill = document.getElementById('progress-fill');
        const progressHandle = document.getElementById('progress-handle');

        if (progressFill) {
            progressFill.style.width = (progress * 100) + '%';
        }

        if (progressHandle) {
            progressHandle.style.left = (progress * 100) + '%';
        }

        // Update time display
        const currentTime = this.currentRecording.events[this.playbackPosition]?.timestamp || 0;
        const totalTime = this.currentRecording.duration;

        document.getElementById('progress-time').textContent =
            `${this.formatTime(currentTime)} / ${this.formatTime(totalTime)}`;
    }

    formatTime(ms) {
        const minutes = Math.floor(ms / 60000);
        const seconds = Math.floor((ms % 60000) / 1000);
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    getTerminalContent() {
        const content = document.getElementById('home-terminal-content');
        return content ? content.innerHTML : '';
    }

    showRecordingList() {
        document.getElementById('recording-list').classList.add('visible');
    }

    hideRecordingList() {
        document.getElementById('recording-list').classList.remove('visible');
    }

    toggleRecordingList() {
        const list = document.getElementById('recording-list');
        list.classList.toggle('visible');
    }

    updateRecordingList() {
        const container = document.getElementById('recording-items');
        if (!container) return;

        container.innerHTML = '';

        this.recordings.forEach(recording => {
            const item = document.createElement('div');
            item.className = 'recording-item';
            item.innerHTML = `
                <div class="recording-info">
                    <div class="recording-name">${recording.name}</div>
                    <div class="recording-meta">
                        ${this.formatTime(recording.duration)} • ${recording.events.length} events
                    </div>
                </div>
                <div class="recording-actions">
                    <button class="recording-action-btn" onclick="window.terminalRecording.playRecording(${JSON.stringify(recording).replace(/"/g, '&quot;')})" title="Play">▶</button>
                    <button class="recording-action-btn" onclick="window.terminalRecording.showExportDialog(${recording.id})" title="Export">📤</button>
                    <button class="recording-action-btn delete" onclick="window.terminalRecording.deleteRecording(${recording.id})" title="Delete">🗑</button>
                </div>
            `;

            container.appendChild(item);
        });
    }

    deleteRecording(id) {
        if (confirm('Are you sure you want to delete this recording?')) {
            this.recordings = this.recordings.filter(r => r.id !== id);
            this.saveRecordingsToStorage();
            this.updateRecordingList();
        }
    }

    showExportDialog(recordingId) {
        this.exportRecordingId = recordingId;
        document.getElementById('export-dialog').classList.add('visible');
        document.getElementById('recording-overlay').classList.add('visible');
    }

    hideExportDialog() {
        document.getElementById('export-dialog').classList.remove('visible');
        document.getElementById('recording-overlay').classList.remove('visible');
    }

    exportRecording() {
        const recording = this.recordings.find(r => r.id === this.exportRecordingId);
        if (!recording) return;

        const format = document.querySelector('input[name="export-format"]:checked').value;

        switch(format) {
            case 'json':
                this.exportAsJSON(recording);
                break;
            case 'text':
                this.exportAsText(recording);
                break;
            case 'gif':
                this.exportAsGIF(recording);
                break;
        }

        this.hideExportDialog();
    }

    exportAsJSON(recording) {
        const data = JSON.stringify(recording, null, 2);
        this.downloadFile(data, `${recording.name}.json`, 'application/json');
    }

    exportAsText(recording) {
        let text = `Terminal Recording: ${recording.name}\n`;
        text += `Duration: ${this.formatTime(recording.duration)}\n`;
        text += `Events: ${recording.events.length}\n`;
        text += `Created: ${new Date(recording.created).toLocaleString()}\n\n`;

        recording.events.forEach((event, index) => {
            text += `[${this.formatTime(event.timestamp)}] ${event.type}`;
            if (event.data) {
                text += `: ${JSON.stringify(event.data)}`;
            }
            text += '\n';
        });

        this.downloadFile(text, `${recording.name}.txt`, 'text/plain');
    }

    exportAsGIF(recording) {
        // This would require a more complex implementation
        // For now, just show a message
        alert('GIF export is not yet implemented. Please use JSON or Text format.');
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        URL.revokeObjectURL(url);
    }

    saveRecordingsToStorage() {
        localStorage.setItem('terminalRecordings', JSON.stringify(this.recordings));
    }

    loadSavedRecordings() {
        const saved = localStorage.getItem('terminalRecordings');
        if (saved) {
            try {
                this.recordings = JSON.parse(saved);
                this.updateRecordingList();
            } catch (e) {
                console.log('Error loading recordings:', e);
            }
        }
    }
}

// Initialize terminal recording
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.home-terminal')) {
        window.terminalRecording = new TerminalRecording();
    }
});
