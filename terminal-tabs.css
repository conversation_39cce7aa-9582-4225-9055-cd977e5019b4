/* Terminal Tabs System */
.terminal-tabs-container {
    position: relative;
    width: 100%;
    height: 100%;
}

/* Tab Bar */
.terminal-tab-bar {
    display: flex;
    background: rgba(0, 0, 0, 0.9);
    border-bottom: 1px solid var(--terminal-border);
    padding: 0 10px;
    gap: 2px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.terminal-tab-bar::-webkit-scrollbar {
    display: none;
}

/* Individual Tab */
.terminal-tab {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid transparent;
    border-bottom: none;
    border-radius: 8px 8px 0 0;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    max-width: 200px;
    position: relative;
    font-family: 'Fira Code', monospace;
    font-size: 12px;
    color: var(--terminal-secondary);
    user-select: none;
}

.terminal-tab:hover {
    background: rgba(0, 0, 0, 0.9);
    border-color: var(--terminal-border);
    color: var(--terminal-text);
}

.terminal-tab.active {
    background: var(--terminal-bg);
    border-color: var(--terminal-border);
    color: var(--terminal-text);
    box-shadow: 0 0 10px var(--terminal-glow);
}

/* Tab Icon */
.tab-icon {
    margin-right: 8px;
    font-size: 14px;
    color: var(--terminal-accent);
}

/* Tab Title */
.tab-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Tab Close Button */
.tab-close {
    margin-left: 8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: transparent;
    border: none;
    color: var(--terminal-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
    opacity: 0;
}

.terminal-tab:hover .tab-close {
    opacity: 1;
}

.tab-close:hover {
    background: rgba(255, 0, 0, 0.2);
    color: #ff4444;
}

/* New Tab Button */
.new-tab-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background: transparent;
    border: 1px solid var(--terminal-border);
    border-radius: 6px;
    color: var(--terminal-accent);
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 10px;
    font-size: 16px;
}

.new-tab-btn:hover {
    background: var(--terminal-border);
    color: var(--terminal-text);
    box-shadow: 0 0 8px var(--terminal-glow);
}

/* Tab Content Area */
.terminal-tabs-content {
    position: relative;
    flex: 1;
    overflow: hidden;
}

.terminal-tab-pane {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
    background: var(--terminal-bg);
}

.terminal-tab-pane.active {
    opacity: 1;
    visibility: visible;
}

/* Tab Types */
.terminal-tab.type-main .tab-icon::before {
    content: '🏠';
}

.terminal-tab.type-security .tab-icon::before {
    content: '🔒';
}

.terminal-tab.type-tools .tab-icon::before {
    content: '🔧';
}

.terminal-tab.type-monitor .tab-icon::before {
    content: '📊';
}

.terminal-tab.type-logs .tab-icon::before {
    content: '📋';
}

.terminal-tab.type-custom .tab-icon::before {
    content: '⚡';
}

/* Tab Status Indicators */
.tab-status {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--terminal-accent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.terminal-tab.has-activity .tab-status {
    opacity: 1;
    animation: tab-activity-pulse 2s infinite;
}

.terminal-tab.has-error .tab-status {
    background: #ff4444;
    opacity: 1;
}

.terminal-tab.is-loading .tab-status {
    background: #ffaa00;
    opacity: 1;
    animation: tab-loading-spin 1s linear infinite;
}

@keyframes tab-activity-pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.5);
        opacity: 0.7;
    }
}

@keyframes tab-loading-spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Tab Drag and Drop */
.terminal-tab.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    z-index: 1000;
}

.terminal-tab.drag-over {
    border-left: 3px solid var(--terminal-accent);
}

/* Tab Context Menu */
.tab-context-menu {
    position: absolute;
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid var(--terminal-border);
    border-radius: 6px;
    padding: 5px 0;
    min-width: 150px;
    z-index: 1001;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.tab-context-item {
    padding: 8px 15px;
    color: var(--terminal-text);
    cursor: pointer;
    font-family: 'Fira Code', monospace;
    font-size: 12px;
    transition: background 0.2s ease;
}

.tab-context-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.tab-context-item.disabled {
    color: var(--terminal-secondary);
    cursor: not-allowed;
}

.tab-context-separator {
    height: 1px;
    background: var(--terminal-border);
    margin: 5px 0;
}

/* Tab Shortcuts Display */
.tab-shortcuts {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid var(--terminal-border);
    border-radius: 4px;
    padding: 5px 10px;
    font-family: 'Fira Code', monospace;
    font-size: 10px;
    color: var(--terminal-secondary);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.terminal-tabs-container:hover .tab-shortcuts {
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .terminal-tab {
        min-width: 80px;
        max-width: 120px;
        padding: 6px 10px;
    }
    
    .tab-title {
        font-size: 11px;
    }
    
    .tab-shortcuts {
        display: none;
    }
}

/* Theme-specific adjustments */
.theme-cyberpunk .terminal-tab.active {
    box-shadow: 0 0 15px var(--terminal-glow);
}

.theme-hacker .terminal-tab:hover {
    background: rgba(255, 0, 0, 0.1);
}

.theme-retro .terminal-tab {
    border-radius: 0;
}

/* Animation for new tabs */
.terminal-tab.new-tab {
    animation: tab-slide-in 0.3s ease-out;
}

@keyframes tab-slide-in {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Tab overflow indicators */
.tab-overflow-left,
.tab-overflow-right {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    background: linear-gradient(90deg, var(--terminal-bg), transparent);
    pointer-events: none;
    z-index: 10;
}

.tab-overflow-left {
    left: 0;
}

.tab-overflow-right {
    right: 0;
    background: linear-gradient(-90deg, var(--terminal-bg), transparent);
}

/* Performance optimizations */
.terminal-tab,
.terminal-tab-pane {
    will-change: transform, opacity;
    transform: translateZ(0);
}
