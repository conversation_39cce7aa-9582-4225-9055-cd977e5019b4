// Home Terminal Interactive System
class HomeTerminal {
    constructor() {
        this.terminalContent = document.getElementById('home-terminal-content');
        this.currentCommand = document.getElementById('current-command');
        this.cursor = document.getElementById('terminal-cursor');
        this.isTyping = false;
        this.currentIndex = 0;
        this.terminalTexts = [];
        this.settings = {
            typingSpeed: 100,
            pauseDuration: 2000,
            soundEnabled: true,
            autoLoop: true
        };

        this.init();
    }

    init() {
        this.loadSettings();
        this.loadTerminalTexts();
        this.setupAudio();
        this.setupMatrixRain();
        this.setupEventListeners();
        this.startTerminalSequence();
    }

    loadSettings() {
        const savedSettings = localStorage.getItem('homeTerminalSettings');
        if (savedSettings) {
            this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
        }
    }

    loadTerminalTexts() {
        const savedTexts = localStorage.getItem('homeTerminalTexts');
        if (savedTexts) {
            this.terminalTexts = JSON.parse(savedTexts);
        } else {
            // Default terminal texts
            this.terminalTexts = [
                {
                    id: 1,
                    type: 'command',
                    content: 'sudo ./initialize_cgg_system.sh --secure-mode',
                    delay: 3000,
                    output: [
                        { type: 'success', text: '🔐 Initializing Code Guard Group Security Suite...' },
                        { type: 'info', text: '📡 Loading advanced threat detection protocols...' },
                        { type: 'success', text: '🛡️ Establishing quantum encryption barriers...' },
                        { type: 'warning', text: '⚡ Activating AI-powered defense systems...' },
                        { type: 'success', text: '✅ CGG Cybersecurity Platform Online' }
                    ]
                },
                {
                    id: 2,
                    type: 'command',
                    content: 'cgg --show-algorithms',
                    delay: 4000,
                    output: [
                        { type: 'info', text: '🧮 Available Algorithms:' },
                        { type: 'output', text: '  ├── AES-256 Encryption' },
                        { type: 'output', text: '  ├── RSA-4096 Key Exchange' },
                        { type: 'output', text: '  ├── SHA-512 Hashing' },
                        { type: 'output', text: '  ├── Elliptic Curve Cryptography' },
                        { type: 'output', text: '  └── Quantum-Resistant Algorithms' }
                    ]
                },
                {
                    id: 3,
                    type: 'command',
                    content: 'echo "Welcome to Code Guard Group - مجموعة حرس الكود"',
                    delay: 3000,
                    output: [
                        { type: 'success', text: 'Welcome to Code Guard Group - مجموعة حرس الكود' },
                        { type: 'info', text: 'Securing the Digital Future | تأمين المستقبل الرقمي' }
                    ]
                },
                {
                    id: 4,
                    type: 'command',
                    content: 'cgg --inspire-quote',
                    delay: 3500,
                    output: [
                        { type: 'warning', text: '💡 "In algorithms we trust, in code we secure"' },
                        { type: 'info', text: '🔒 "Every line of code is a shield against chaos"' },
                        { type: 'success', text: '🚀 "Innovation through secure computation"' }
                    ]
                },
                {
                    id: 5,
                    type: 'command',
                    content: 'cgg --system-status',
                    delay: 2500,
                    output: [
                        { type: 'success', text: '🟢 All systems operational' },
                        { type: 'info', text: '📊 Monitoring 24/7 active' },
                        { type: 'success', text: '🔐 Security protocols engaged' },
                        { type: 'warning', text: '⚠️  0 threats detected today' }
                    ]
                },
                {
                    id: 6,
                    type: 'command',
                    content: 'cat /etc/algorithms/inspiration.txt',
                    delay: 4000,
                    output: [
                        { type: 'info', text: '💭 "Code is poetry written in logic"' },
                        { type: 'success', text: '🔢 "Every algorithm tells a story"' },
                        { type: 'warning', text: '⚡ "Optimization is the art of perfection"' },
                        { type: 'info', text: '🧠 "Think recursive, act iterative"' }
                    ]
                },
                {
                    id: 7,
                    type: 'command',
                    content: 'cgg --show-team-motto',
                    delay: 3500,
                    output: [
                        { type: 'success', text: '👥 Code Guard Group Team Philosophy:' },
                        { type: 'info', text: '🛡️ "We don\'t just write code, we craft digital shields"' },
                        { type: 'warning', text: '🔐 "Security is not a feature, it\'s a foundation"' },
                        { type: 'success', text: '🚀 "Innovation through secure computation"' }
                    ]
                },
                {
                    id: 8,
                    type: 'command',
                    content: 'grep -r "wisdom" /var/log/cgg/',
                    delay: 3000,
                    output: [
                        { type: 'output', text: '/var/log/cgg/daily.log: "The best algorithm is the one you understand"' },
                        { type: 'output', text: '/var/log/cgg/security.log: "Complexity is the enemy of security"' },
                        { type: 'output', text: '/var/log/cgg/team.log: "Code together, secure forever"' }
                    ]
                },
                {
                    id: 9,
                    type: 'command',
                    content: 'cgg --random-fact',
                    delay: 2800,
                    output: [
                        { type: 'info', text: '🤓 Did you know?' },
                        { type: 'success', text: '📈 The first computer bug was an actual bug found in 1947' },
                        { type: 'warning', text: '🔢 There are 10 types of people: those who understand binary and those who don\'t' },
                        { type: 'info', text: '💡 The term "algorithm" comes from Al-Khwarizmi, a 9th-century mathematician' }
                    ]
                }
            ];
            this.saveTerminalTexts();
        }
    }

    saveTerminalTexts() {
        localStorage.setItem('homeTerminalTexts', JSON.stringify(this.terminalTexts));
    }

    setupAudio() {
        try {
            const AudioContextClass = window.AudioContext || window['webkitAudioContext'];
            this.audioContext = new AudioContextClass();
        } catch (e) {
            console.log('Web Audio API not supported');
            this.settings.soundEnabled = false;
        }
    }

    playTypingSound() {
        if (!this.settings.soundEnabled || !this.audioContext) return;

        // Create a more realistic mechanical keyboard sound
        const oscillator1 = this.audioContext.createOscillator();
        const oscillator2 = this.audioContext.createOscillator();
        const noiseBuffer = this.createNoiseBuffer();
        const noiseSource = this.audioContext.createBufferSource();

        const gainNode1 = this.audioContext.createGain();
        const gainNode2 = this.audioContext.createGain();
        const noiseGain = this.audioContext.createGain();
        const filter1 = this.audioContext.createBiquadFilter();
        const filter2 = this.audioContext.createBiquadFilter();
        const masterGain = this.audioContext.createGain();

        // Connect the audio graph
        oscillator1.connect(filter1);
        oscillator2.connect(filter2);
        noiseSource.buffer = noiseBuffer;
        noiseSource.connect(noiseGain);

        filter1.connect(gainNode1);
        filter2.connect(gainNode2);
        gainNode1.connect(masterGain);
        gainNode2.connect(masterGain);
        noiseGain.connect(masterGain);
        masterGain.connect(this.audioContext.destination);

        // Key press frequencies (more realistic)
        const keyFrequencies = [200, 250, 300, 350, 400, 450];
        const baseFreq = keyFrequencies[Math.floor(Math.random() * keyFrequencies.length)];

        // First oscillator - key press
        oscillator1.frequency.setValueAtTime(baseFreq, this.audioContext.currentTime);
        oscillator1.type = 'sawtooth';

        // Second oscillator - harmonic
        oscillator2.frequency.setValueAtTime(baseFreq * 2.5, this.audioContext.currentTime);
        oscillator2.type = 'triangle';

        // Filters for realistic timbre
        filter1.type = 'bandpass';
        filter1.frequency.setValueAtTime(baseFreq * 3, this.audioContext.currentTime);
        filter1.Q.setValueAtTime(2, this.audioContext.currentTime);

        filter2.type = 'highpass';
        filter2.frequency.setValueAtTime(baseFreq * 4, this.audioContext.currentTime);
        filter2.Q.setValueAtTime(1, this.audioContext.currentTime);

        // Gain envelopes for realistic attack/decay
        gainNode1.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode1.gain.linearRampToValueAtTime(0.015, this.audioContext.currentTime + 0.005);
        gainNode1.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.05);

        gainNode2.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode2.gain.linearRampToValueAtTime(0.008, this.audioContext.currentTime + 0.003);
        gainNode2.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.03);

        // Noise for mechanical click
        noiseGain.gain.setValueAtTime(0, this.audioContext.currentTime);
        noiseGain.gain.linearRampToValueAtTime(0.005, this.audioContext.currentTime + 0.001);
        noiseGain.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.02);

        masterGain.gain.setValueAtTime(1, this.audioContext.currentTime);

        // Start and stop
        oscillator1.start(this.audioContext.currentTime);
        oscillator2.start(this.audioContext.currentTime);
        noiseSource.start(this.audioContext.currentTime);

        oscillator1.stop(this.audioContext.currentTime + 0.05);
        oscillator2.stop(this.audioContext.currentTime + 0.03);
        noiseSource.stop(this.audioContext.currentTime + 0.02);
    }

    createNoiseBuffer() {
        const bufferSize = this.audioContext.sampleRate * 0.02; // 20ms of noise
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const output = buffer.getChannelData(0);

        for (let i = 0; i < bufferSize; i++) {
            output[i] = Math.random() * 2 - 1;
        }

        return buffer;
    }

    playCommandSound() {
        if (!this.settings.soundEnabled || !this.audioContext) return;

        // Create a more sophisticated command execution sound
        const oscillator1 = this.audioContext.createOscillator();
        const oscillator2 = this.audioContext.createOscillator();
        const gainNode1 = this.audioContext.createGain();
        const gainNode2 = this.audioContext.createGain();
        const masterGain = this.audioContext.createGain();

        oscillator1.connect(gainNode1);
        oscillator2.connect(gainNode2);
        gainNode1.connect(masterGain);
        gainNode2.connect(masterGain);
        masterGain.connect(this.audioContext.destination);

        // First tone - command start
        oscillator1.frequency.setValueAtTime(600, this.audioContext.currentTime);
        oscillator1.frequency.exponentialRampToValueAtTime(800, this.audioContext.currentTime + 0.1);
        oscillator1.type = 'sine';

        // Second tone - command completion
        oscillator2.frequency.setValueAtTime(400, this.audioContext.currentTime + 0.15);
        oscillator2.frequency.exponentialRampToValueAtTime(300, this.audioContext.currentTime + 0.4);
        oscillator2.type = 'triangle';

        // Gain envelopes
        gainNode1.gain.setValueAtTime(0.08, this.audioContext.currentTime);
        gainNode1.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.15);

        gainNode2.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode2.gain.setValueAtTime(0.06, this.audioContext.currentTime + 0.15);
        gainNode2.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.4);

        masterGain.gain.setValueAtTime(1, this.audioContext.currentTime);

        oscillator1.start(this.audioContext.currentTime);
        oscillator1.stop(this.audioContext.currentTime + 0.15);

        oscillator2.start(this.audioContext.currentTime + 0.15);
        oscillator2.stop(this.audioContext.currentTime + 0.4);
    }

    playSuccessSound() {
        if (!this.settings.soundEnabled || !this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        // Success notification sound
        oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(1200, this.audioContext.currentTime + 0.2);
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0.05, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.2);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.2);
    }

    setupMatrixRain() {
        const matrixContainer = document.getElementById('matrix-rain');
        const chars = '01CGG{}[]()<>+-*/=!@#$%^&*_|\\:;"\'.,?`~アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';

        // Create matrix characters with more variety
        for (let i = 0; i < 80; i++) {
            const char = document.createElement('div');
            char.className = 'matrix-char';
            char.textContent = chars[Math.floor(Math.random() * chars.length)];
            char.style.left = Math.random() * 100 + '%';
            char.style.animationDuration = (Math.random() * 4 + 3) + 's';
            char.style.animationDelay = Math.random() * 3 + 's';
            char.style.fontSize = (Math.random() * 6 + 10) + 'px';
            char.style.opacity = Math.random() * 0.7 + 0.3;

            // Add some special highlighting for CGG characters
            if (char.textContent === 'C' || char.textContent === 'G') {
                char.style.color = '#00ffff';
                char.style.textShadow = '0 0 10px #00ffff';
            }

            matrixContainer.appendChild(char);
        }

        // Periodically update matrix characters
        setInterval(() => {
            const matrixChars = matrixContainer.querySelectorAll('.matrix-char');
            matrixChars.forEach(char => {
                if (Math.random() < 0.1) { // 10% chance to change character
                    char.textContent = chars[Math.floor(Math.random() * chars.length)];
                    if (char.textContent === 'C' || char.textContent === 'G') {
                        char.style.color = '#00ffff';
                        char.style.textShadow = '0 0 10px #00ffff';
                    } else {
                        char.style.color = '#00ff41';
                        char.style.textShadow = 'none';
                    }
                }
            });
        }, 2000);
    }

    setupEventListeners() {
        // Click anywhere to interact
        document.addEventListener('click', (e) => {
            if (this.audioContext && this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }

            // Create click effect
            this.createClickEffect(e.clientX, e.clientY);
        });

        // Keyboard interaction
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !this.isTyping) {
                this.playCommandSound();
                this.addInteractiveMessage();
            }

            if (e.key === ' ' && !this.isTyping) {
                e.preventDefault();
                this.playSuccessSound();
                this.addRandomFact();
            }
        });

        // Update stats periodically
        this.updateStats();
        setInterval(() => this.updateStats(), 5000);

        // Hide navigation hint after first interaction
        setTimeout(() => {
            const hint = document.querySelector('.navigation-hint');
            if (hint) {
                hint.style.animation = 'fadeOut 1s ease-out forwards';
                setTimeout(() => hint.style.display = 'none', 1000);
            }
        }, 10000);
    }

    createClickEffect(x, y) {
        const effect = document.createElement('div');
        effect.style.position = 'fixed';
        effect.style.left = x + 'px';
        effect.style.top = y + 'px';
        effect.style.width = '20px';
        effect.style.height = '20px';
        effect.style.border = '2px solid #00ff41';
        effect.style.borderRadius = '50%';
        effect.style.pointerEvents = 'none';
        effect.style.zIndex = '9999';
        effect.style.animation = 'clickRipple 0.6s ease-out forwards';

        document.body.appendChild(effect);

        setTimeout(() => {
            document.body.removeChild(effect);
        }, 600);
    }

    addInteractiveMessage() {
        const messages = [
            '🎯 Interactive mode activated!',
            '⚡ System responding to user input...',
            '🔍 Scanning for user preferences...',
            '🛡️ Security protocols engaged!',
            '🚀 Ready for next command!'
        ];

        const randomMessage = messages[Math.floor(Math.random() * messages.length)];
        this.addToHistory('info', randomMessage);
    }

    addRandomFact() {
        const facts = [
            '💡 Fun fact: The first computer programmer was Ada Lovelace in 1843!',
            '🔢 Binary code uses only 0s and 1s to represent all data!',
            '🌐 The internet was originally called ARPANET!',
            '🔐 The word "bug" in programming comes from an actual bug found in a computer!',
            '⚡ The first computer virus was created in 1971!',
            '🧠 AI algorithms can now write their own code!'
        ];

        const randomFact = facts[Math.floor(Math.random() * facts.length)];
        this.addToHistory('success', randomFact);
    }

    updateStats() {
        const uptimeElement = document.getElementById('uptime-counter');
        const threatsElement = document.getElementById('threats-counter');
        const clientsElement = document.getElementById('clients-counter');

        if (uptimeElement) {
            const uptime = (99.9 + Math.random() * 0.09).toFixed(2);
            uptimeElement.textContent = uptime + '%';
        }

        if (threatsElement) {
            const threats = Math.floor(1000 + Math.random() * 500);
            threatsElement.textContent = threats + '+';
        }

        if (clientsElement) {
            const clients = Math.floor(50 + Math.random() * 20);
            clientsElement.textContent = clients + '+';
        }
    }

    async startTerminalSequence() {
        if (this.terminalTexts.length === 0) return;

        while (true) {
            for (let i = 0; i < this.terminalTexts.length; i++) {
                const textData = this.terminalTexts[i];
                await this.typeCommand(textData);
                await this.delay(textData.delay || 2000);
            }

            if (!this.settings.autoLoop) break;
            await this.delay(3000); // Pause before restarting
        }
    }

    async typeCommand(textData) {
        this.isTyping = true;
        this.currentCommand.textContent = '';

        // Type the command
        for (let i = 0; i < textData.content.length; i++) {
            this.currentCommand.textContent += textData.content[i];
            this.playTypingSound();
            await this.delay(this.settings.typingSpeed);
        }

        // Play command execution sound
        this.playCommandSound();
        await this.delay(500);

        // Add command to history
        this.addToHistory('command', textData.content);

        // Show output if exists
        if (textData.output && textData.output.length > 0) {
            for (const outputLine of textData.output) {
                await this.delay(200);
                this.addToHistory(outputLine.type, outputLine.text);
            }
        }

        // Clear current command
        this.currentCommand.textContent = '';
        this.isTyping = false;
    }

    addToHistory(type, text) {
        const line = document.createElement('div');
        line.className = `terminal-line ${type}`;

        if (type === 'command') {
            line.innerHTML = `<span class="terminal-prompt">root@cgg:~#</span> ${text}`;
        } else {
            line.textContent = text;
        }

        this.terminalContent.appendChild(line);

        // Play appropriate sound for different types
        if (type === 'success') {
            setTimeout(() => this.playSuccessSound(), 100);
        }

        // Auto-scroll to bottom
        this.terminalContent.scrollTop = this.terminalContent.scrollHeight;

        // Limit history to prevent memory issues
        if (this.terminalContent.children.length > 100) {
            this.terminalContent.removeChild(this.terminalContent.firstChild);
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Public methods for admin management
    addTerminalText(textData) {
        textData.id = Date.now();
        this.terminalTexts.push(textData);
        this.saveTerminalTexts();
    }

    updateTerminalText(id, textData) {
        const index = this.terminalTexts.findIndex(text => text.id === id);
        if (index !== -1) {
            this.terminalTexts[index] = { ...this.terminalTexts[index], ...textData };
            this.saveTerminalTexts();
        }
    }

    deleteTerminalText(id) {
        this.terminalTexts = this.terminalTexts.filter(text => text.id !== id);
        this.saveTerminalTexts();
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        localStorage.setItem('homeTerminalSettings', JSON.stringify(this.settings));
    }

    getTerminalTexts() {
        return this.terminalTexts;
    }

    getSettings() {
        return this.settings;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.homeTerminal = new HomeTerminal();
});
