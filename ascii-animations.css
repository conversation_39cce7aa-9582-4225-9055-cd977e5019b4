/* ASCII Animations System */

/* Base ASCII animation container */
.ascii-animation {
    font-family: 'Fira Code', monospace;
    white-space: pre;
    line-height: 1.1;
    color: var(--terminal-text);
    text-shadow: 0 0 8px var(--terminal-glow);
    position: relative;
    overflow: hidden;
}

/* Animated ASCII Art */
.ascii-animated {
    animation: ascii-glow 3s ease-in-out infinite alternate;
}

@keyframes ascii-glow {
    0% {
        text-shadow: 
            0 0 5px var(--terminal-glow),
            0 0 10px var(--terminal-glow);
        transform: scale(1);
    }
    50% {
        text-shadow: 
            0 0 10px var(--terminal-glow),
            0 0 20px var(--terminal-glow),
            0 0 30px var(--terminal-glow);
        transform: scale(1.02);
    }
    100% {
        text-shadow: 
            0 0 15px var(--terminal-glow),
            0 0 25px var(--terminal-glow),
            0 0 35px var(--terminal-glow);
        transform: scale(1.05);
    }
}

/* Typing animation for ASCII */
.ascii-typing {
    overflow: hidden;
    border-right: 2px solid var(--terminal-accent);
    animation: ascii-type 4s steps(40) 1s forwards, ascii-cursor-blink 1s infinite;
    width: 0;
}

@keyframes ascii-type {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes ascii-cursor-blink {
    0%, 50% {
        border-color: var(--terminal-accent);
    }
    51%, 100% {
        border-color: transparent;
    }
}

/* Matrix-style character rain in ASCII */
.ascii-matrix-rain {
    position: relative;
}

.ascii-matrix-rain::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 10% 20%, var(--terminal-text) 1px, transparent 1px),
        radial-gradient(circle at 80% 80%, var(--terminal-text) 1px, transparent 1px),
        radial-gradient(circle at 40% 40%, var(--terminal-text) 1px, transparent 1px);
    background-size: 20px 20px, 30px 30px, 25px 25px;
    opacity: 0.1;
    animation: ascii-matrix-fall 10s linear infinite;
}

@keyframes ascii-matrix-fall {
    0% {
        background-position: 0 0, 0 0, 0 0;
    }
    100% {
        background-position: 0 100px, 0 150px, 0 125px;
    }
}

/* Glitch effect for ASCII */
.ascii-glitch {
    position: relative;
}

.ascii-glitch::before,
.ascii-glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
}

.ascii-glitch::before {
    animation: ascii-glitch-1 0.5s infinite linear alternate-reverse;
    color: #ff0000;
    z-index: -1;
}

.ascii-glitch::after {
    animation: ascii-glitch-2 0.5s infinite linear alternate-reverse;
    color: #00ffff;
    z-index: -2;
}

@keyframes ascii-glitch-1 {
    0% {
        transform: translateX(0);
        clip-path: polygon(0 0%, 100% 0%, 100% 20%, 0 20%);
    }
    20% {
        transform: translateX(-2px);
        clip-path: polygon(0 20%, 100% 20%, 100% 40%, 0 40%);
    }
    40% {
        transform: translateX(2px);
        clip-path: polygon(0 40%, 100% 40%, 100% 60%, 0 60%);
    }
    60% {
        transform: translateX(-1px);
        clip-path: polygon(0 60%, 100% 60%, 100% 80%, 0 80%);
    }
    80% {
        transform: translateX(1px);
        clip-path: polygon(0 80%, 100% 80%, 100% 100%, 0 100%);
    }
    100% {
        transform: translateX(0);
        clip-path: polygon(0 0%, 100% 0%, 100% 100%, 0 100%);
    }
}

@keyframes ascii-glitch-2 {
    0% {
        transform: translateX(0);
        clip-path: polygon(0 15%, 100% 15%, 100% 30%, 0 30%);
    }
    25% {
        transform: translateX(2px);
        clip-path: polygon(0 30%, 100% 30%, 100% 45%, 0 45%);
    }
    50% {
        transform: translateX(-2px);
        clip-path: polygon(0 45%, 100% 45%, 100% 60%, 0 60%);
    }
    75% {
        transform: translateX(1px);
        clip-path: polygon(0 60%, 100% 60%, 100% 75%, 0 75%);
    }
    100% {
        transform: translateX(0);
        clip-path: polygon(0 75%, 100% 75%, 100% 90%, 0 90%);
    }
}

/* Pulse animation */
.ascii-pulse {
    animation: ascii-pulse-effect 2s ease-in-out infinite;
}

@keyframes ascii-pulse-effect {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

/* Rotate animation */
.ascii-rotate {
    animation: ascii-rotate-effect 10s linear infinite;
    transform-origin: center;
}

@keyframes ascii-rotate-effect {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Bounce animation */
.ascii-bounce {
    animation: ascii-bounce-effect 2s ease-in-out infinite;
}

@keyframes ascii-bounce-effect {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Wave animation */
.ascii-wave {
    animation: ascii-wave-effect 3s ease-in-out infinite;
}

@keyframes ascii-wave-effect {
    0%, 100% {
        transform: skewX(0deg);
    }
    25% {
        transform: skewX(5deg);
    }
    75% {
        transform: skewX(-5deg);
    }
}

/* Fade in animation */
.ascii-fade-in {
    animation: ascii-fade-in-effect 2s ease-out forwards;
    opacity: 0;
}

@keyframes ascii-fade-in-effect {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Slide in from left */
.ascii-slide-left {
    animation: ascii-slide-left-effect 1s ease-out forwards;
    transform: translateX(-100%);
}

@keyframes ascii-slide-left-effect {
    to {
        transform: translateX(0);
    }
}

/* Slide in from right */
.ascii-slide-right {
    animation: ascii-slide-right-effect 1s ease-out forwards;
    transform: translateX(100%);
}

@keyframes ascii-slide-right-effect {
    to {
        transform: translateX(0);
    }
}

/* Zoom in animation */
.ascii-zoom-in {
    animation: ascii-zoom-in-effect 1s ease-out forwards;
    transform: scale(0);
}

@keyframes ascii-zoom-in-effect {
    to {
        transform: scale(1);
    }
}

/* Flip animation */
.ascii-flip {
    animation: ascii-flip-effect 2s ease-in-out infinite;
}

@keyframes ascii-flip-effect {
    0%, 100% {
        transform: rotateY(0);
    }
    50% {
        transform: rotateY(180deg);
    }
}

/* Shake animation */
.ascii-shake {
    animation: ascii-shake-effect 0.5s ease-in-out infinite;
}

@keyframes ascii-shake-effect {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-2px);
    }
    75% {
        transform: translateX(2px);
    }
}

/* Color cycle animation */
.ascii-color-cycle {
    animation: ascii-color-cycle-effect 5s linear infinite;
}

@keyframes ascii-color-cycle-effect {
    0% { color: var(--terminal-text); }
    16.66% { color: #ff0080; }
    33.33% { color: #00ffff; }
    50% { color: #ffaa00; }
    66.66% { color: #ff3333; }
    83.33% { color: #00ff41; }
    100% { color: var(--terminal-text); }
}

/* Breathing animation */
.ascii-breathe {
    animation: ascii-breathe-effect 4s ease-in-out infinite;
}

@keyframes ascii-breathe-effect {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

/* Specific ASCII art animations */
.ascii-logo-cgg {
    animation: 
        ascii-glow 3s ease-in-out infinite alternate,
        ascii-breathe 6s ease-in-out infinite;
}

.ascii-loading {
    position: relative;
}

.ascii-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent 0%,
        var(--terminal-glow) 50%,
        transparent 100%
    );
    animation: ascii-loading-sweep 2s linear infinite;
    opacity: 0.3;
}

@keyframes ascii-loading-sweep {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Interactive ASCII animations */
.ascii-interactive {
    cursor: pointer;
    transition: all 0.3s ease;
}

.ascii-interactive:hover {
    animation: ascii-pulse-effect 0.5s ease-in-out;
    text-shadow: 
        0 0 10px var(--terminal-glow),
        0 0 20px var(--terminal-glow),
        0 0 30px var(--terminal-glow);
}

/* Theme-specific ASCII animations */
.theme-cyberpunk .ascii-animated {
    animation-duration: 1s;
}

.theme-hacker .ascii-glitch {
    animation-duration: 0.3s;
}

.theme-retro .ascii-animated {
    animation-duration: 4s;
}

/* Performance optimizations */
.ascii-animation,
.ascii-animated,
.ascii-glitch {
    will-change: transform, opacity, color;
    transform: translateZ(0);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .ascii-animated,
    .ascii-glitch,
    .ascii-pulse,
    .ascii-rotate,
    .ascii-bounce,
    .ascii-wave,
    .ascii-flip,
    .ascii-shake,
    .ascii-color-cycle,
    .ascii-breathe {
        animation: none;
    }
    
    .ascii-animated {
        text-shadow: 
            0 0 5px var(--terminal-glow),
            0 0 10px var(--terminal-glow);
    }
}
