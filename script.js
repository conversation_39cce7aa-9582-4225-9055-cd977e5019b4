// CGG Enhanced Animation System
class CGGAnimation {
    constructor() {
        this.isPlaying = false;
        this.soundEnabled = true;
        this.canvas = document.getElementById('code-canvas');
        this.ctx = this.canvas ? this.canvas.getContext('2d') : null;
        this.particles = [];
        this.codeStreams = [];
        this.animationStarted = false;

        this.init();
        this.setupEventListeners();
    }

    init() {
        // Setup canvas
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());

        // Initialize particles for code streams
        this.initCodeStreams();

        // Setup audio context for sound effects
        this.setupAudio();
    }

    resizeCanvas() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }

    setupEventListeners() {
        const replayBtn = document.getElementById('replay-btn');
        const soundToggle = document.getElementById('sound-toggle');

        replayBtn.addEventListener('click', () => this.restartAnimation());
        soundToggle.addEventListener('click', () => this.toggleSound());

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                this.restartAnimation();
            }
            if (e.code === 'KeyM') {
                e.preventDefault();
                this.toggleSound();
            }
        });
    }

    setupAudio() {
        // Create audio context for procedural sound generation
        try {
            const AudioContextClass = window.AudioContext || window['webkitAudioContext'];
            this.audioContext = new AudioContextClass();
        } catch (e) {
            console.log('Web Audio API not supported');
            this.soundEnabled = false;
        }
    }

    playTypingSound() {
        if (!this.soundEnabled || !this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(800 + Math.random() * 200, this.audioContext.currentTime);
        oscillator.type = 'square';

        gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.1);
    }

    playGlitchSound() {
        if (!this.soundEnabled || !this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        const filter = this.audioContext.createBiquadFilter();

        oscillator.connect(filter);
        filter.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(50, this.audioContext.currentTime + 0.5);
        oscillator.type = 'sawtooth';

        filter.type = 'lowpass';
        filter.frequency.setValueAtTime(1000, this.audioContext.currentTime);

        gainNode.gain.setValueAtTime(0.2, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.5);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.5);
    }

    playShieldSound() {
        if (!this.soundEnabled || !this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(400, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(800, this.audioContext.currentTime + 1);
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0.15, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 1);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 1);
    }

    initCodeStreams() {
        const codeChars = '01CGG{}[]()<>+-*/=!@#$%^&*_|\\:;"\'.,?`~';

        for (let i = 0; i < 50; i++) {
            this.codeStreams.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                char: codeChars[Math.floor(Math.random() * codeChars.length)],
                speed: 2 + Math.random() * 5,
                opacity: Math.random(),
                size: 12 + Math.random() * 8,
                color: Math.random() > 0.5 ? '#00ffff' : '#ff00ff',
                targetX: this.canvas.width / 2,
                targetY: this.canvas.height / 2,
                active: false
            });
        }
    }

    animateCodeStreams() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        this.codeStreams.forEach(stream => {
            if (!stream.active) return;

            // Move towards center
            const dx = stream.targetX - stream.x;
            const dy = stream.targetY - stream.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance > 5) {
                stream.x += (dx / distance) * stream.speed;
                stream.y += (dy / distance) * stream.speed;
                stream.opacity = Math.max(0, stream.opacity - 0.01);
            } else {
                stream.active = false;
            }

            // Draw character
            this.ctx.save();
            this.ctx.globalAlpha = stream.opacity;
            this.ctx.fillStyle = stream.color;
            this.ctx.font = `${stream.size}px 'Courier New', monospace`;
            this.ctx.fillText(stream.char, stream.x, stream.y);
            this.ctx.restore();
        });
    }

    typeText(element, text, speed = 100) {
        return new Promise((resolve) => {
            let i = 0;
            element.textContent = '';

            const typeInterval = setInterval(() => {
                if (i < text.length) {
                    element.textContent += text.charAt(i);
                    this.playTypingSound();
                    i++;
                } else {
                    clearInterval(typeInterval);
                    resolve();
                }
            }, speed);
        });
    }

    async startAnimation() {
        if (this.isPlaying) return;
        this.isPlaying = true;

        // Reset all elements
        this.resetElements();

        // Phase 1: Terminal typing (0-3s)
        await this.terminalPhase();

        // Phase 2: Glitch effects (3-4s)
        await this.glitchPhase();

        // Phase 3: Code streams (4-5s)
        await this.codeStreamPhase();

        // Phase 4: Logo appearance (5-7s)
        await this.logoPhase();

        // Phase 5: Shield formation (7-8s)
        await this.shieldPhase();

        // Phase 6: Final text (8-10s)
        await this.finalPhase();

        this.isPlaying = false;
    }

    resetElements() {
        // Reset terminal
        const terminal = document.getElementById('terminal');
        const typingText = document.getElementById('typing-text');
        const terminalOutput = document.getElementById('terminal-output');
        const glitchOverlay = document.getElementById('glitch-overlay');
        const logoContainer = document.getElementById('logo-container');
        const digitalShield = document.getElementById('digital-shield');
        const companyText = document.getElementById('company-text');

        terminal.style.opacity = '1';
        terminal.style.animation = 'none';
        typingText.textContent = '';
        terminalOutput.innerHTML = '';
        glitchOverlay.style.opacity = '0';
        logoContainer.style.opacity = '0';
        logoContainer.style.animation = 'none';
        digitalShield.style.opacity = '0';
        digitalShield.style.animation = 'none';
        companyText.style.opacity = '0';
        companyText.style.animation = 'none';

        // Reset canvas
        this.canvas.style.opacity = '0';
        this.codeStreams.forEach(stream => {
            stream.active = false;
            stream.x = Math.random() * this.canvas.width;
            stream.y = Math.random() * this.canvas.height;
            stream.opacity = Math.random();
        });
    }

    async terminalPhase() {
        const typingText = document.getElementById('typing-text');
        const terminalOutput = document.getElementById('terminal-output');

        // Type the command
        await this.typeText(typingText, 'sudo ./initialize_cgg_system.sh --secure-mode', 80);
        await this.delay(500);

        // Show output
        terminalOutput.innerHTML = `
            <div style="color: #00ff41;">🔐 Initializing Code Guard Group Security Suite...</div>
            <div style="color: #ffff00;">📡 Loading advanced threat detection protocols...</div>
            <div style="color: #00ffff;">🛡️ Establishing quantum encryption barriers...</div>
            <div style="color: #ff00ff;">⚡ Activating AI-powered defense systems...</div>
            <div style="color: #50fa7b;">✅ CGG Cybersecurity Platform Online</div>
            <div style="color: #8be9fd;">🌐 Welcome to the future of digital security</div>
        `;

        await this.delay(2000);

        // Fade out terminal
        const terminal = document.getElementById('intro-terminal');
        if (terminal) {
            terminal.style.animation = 'terminalFadeOut 1s ease-in-out forwards';
        }

        await this.delay(1000);
    }

    async glitchPhase() {
        const glitchOverlay = document.getElementById('glitch-overlay');

        glitchOverlay.style.opacity = '1';
        this.playGlitchSound();

        await this.delay(1000);

        glitchOverlay.style.opacity = '0';
    }

    async codeStreamPhase() {
        this.canvas.style.opacity = '1';

        // Activate code streams
        this.codeStreams.forEach((stream, index) => {
            setTimeout(() => {
                stream.active = true;
                stream.opacity = 1;
            }, index * 20);
        });

        // Animate streams
        const streamAnimation = () => {
            this.animateCodeStreams();
            if (this.codeStreams.some(stream => stream.active)) {
                requestAnimationFrame(streamAnimation);
            } else {
                this.canvas.style.opacity = '0';
            }
        };

        requestAnimationFrame(streamAnimation);
        await this.delay(1500);
    }

    async logoPhase() {
        const logoContainer = document.getElementById('logo-container');
        logoContainer.style.animation = 'logoAppear 2s ease-out forwards';

        await this.delay(2000);
    }

    async shieldPhase() {
        const digitalShield = document.getElementById('digital-shield');
        digitalShield.style.animation = 'shieldAppear 1.5s ease-out forwards';
        this.playShieldSound();

        await this.delay(1500);

        // Add pulse effect
        digitalShield.style.animation += ', shieldPulse 2s ease-in-out 2s';
    }

    async finalPhase() {
        const companyText = document.getElementById('company-text');
        if (companyText) {
            companyText.style.animation = 'textAppear 1s ease-out forwards';
        }

        await this.delay(2000);

        // Show home content after animation
        const homeContent = document.getElementById('home-content');
        if (homeContent) {
            homeContent.style.animation = 'homeContentAppear 1s ease-out forwards';
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    restartAnimation() {
        if (!this.isPlaying) {
            this.startAnimation();
        }
    }

    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        const soundToggle = document.getElementById('sound-toggle');
        if (soundToggle) {
            const icon = soundToggle.querySelector('span');
            if (icon) {
                icon.textContent = this.soundEnabled ? '🔊' : '🔇';
            }
        }
    }

    // Public method to start animation (called by navigation manager)
    triggerAnimation() {
        if (!this.animationStarted) {
            this.animationStarted = true;
            this.startAnimation();
        }
    }
}

// Initialize animation when page loads
document.addEventListener('DOMContentLoaded', () => {
    window['cggAnimation'] = new CGGAnimation();
});
