<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CGG - Code Guard Group | Animated Logo</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Terminal Phase -->
        <div class="terminal-container" id="terminal">
            <div class="terminal-header">
                <div class="terminal-buttons">
                    <span class="btn close"></span>
                    <span class="btn minimize"></span>
                    <span class="btn maximize"></span>
                </div>
                <div class="terminal-title">CGG Terminal v2.1.0</div>
            </div>
            <div class="terminal-body">
                <div class="terminal-line">
                    <span class="prompt">user@cgg:~$</span>
                    <span class="command" id="typing-text"></span>
                    <span class="cursor" id="cursor">|</span>
                </div>
                <div class="terminal-output" id="terminal-output"></div>
            </div>
        </div>

        <!-- Glitch Overlay -->
        <div class="glitch-overlay" id="glitch-overlay">
            <div class="glitch-lines"></div>
            <div class="glitch-noise"></div>
        </div>

        <!-- Code Streams -->
        <canvas id="code-canvas" class="code-canvas"></canvas>

        <!-- Main Logo Container -->
        <div class="logo-container" id="logo-container">
            <!-- CGG Logo -->
            <div class="cgg-logo" id="cgg-logo">
                <div class="logo-letter" data-letter="C">C</div>
                <div class="logo-letter" data-letter="G">G</div>
                <div class="logo-letter" data-letter="G">G</div>
            </div>

            <!-- Digital Shield -->
            <div class="digital-shield" id="digital-shield">
                <div class="shield-layer shield-outer"></div>
                <div class="shield-layer shield-middle"></div>
                <div class="shield-layer shield-inner"></div>
                <div class="shield-circuits">
                    <div class="circuit-line circuit-1"></div>
                    <div class="circuit-line circuit-2"></div>
                    <div class="circuit-line circuit-3"></div>
                    <div class="circuit-line circuit-4"></div>
                </div>
                <div class="shield-particles" id="shield-particles"></div>
            </div>

            <!-- Company Text -->
            <div class="company-text" id="company-text">
                <div class="text-english">CODE GUARD GROUP</div>
                <div class="text-arabic">مجموعة حرس الكود</div>
                <div class="text-tagline">SECURING THE DIGITAL FUTURE</div>
            </div>
        </div>

        <!-- Scanlines Effect -->
        <div class="scanlines"></div>

        <!-- Data Pulses -->
        <div class="data-pulses">
            <div class="pulse pulse-1"></div>
            <div class="pulse pulse-2"></div>
            <div class="pulse pulse-3"></div>
        </div>

        <!-- Background Matrix -->
        <div class="matrix-bg" id="matrix-bg"></div>

        <!-- Control Panel -->
        <div class="control-panel">
            <button id="replay-btn" class="control-btn">
                <span>⟲</span> REPLAY
            </button>
            <button id="sound-toggle" class="control-btn">
                <span>🔊</span> SOUND
            </button>
        </div>
    </div>

    <!-- Audio Elements -->
    <audio id="typing-sound" preload="auto">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>
    <audio id="glitch-sound" preload="auto">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>

    <script src="script.js"></script>
</body>
</html>
