// Navigation and Page Management System for CGG Website
class NavigationManager {
    constructor() {
        this.currentPage = 'home';
        this.isAnimating = false;
        this.loadingScreen = document.getElementById('loading-screen');
        this.mainNav = document.getElementById('main-nav');
        this.pages = document.querySelectorAll('.page-content');
        this.navLinks = document.querySelectorAll('.nav-link');
        this.navToggle = document.getElementById('nav-toggle');
        this.navMenu = document.getElementById('nav-menu');
        this.terminal = document.getElementById('interactive-terminal');

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.showLoadingScreen();
        this.preloadAssets();
    }

    setupEventListeners() {
        // Navigation links
        this.navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.getAttribute('data-page');

                if (page) {
                    this.navigateToPage(page);
                } else if (link.classList.contains('terminal-toggle')) {
                    this.toggleTerminal();
                }
            });
        });

        // Mobile menu toggle
        this.navToggle.addEventListener('click', () => {
            this.toggleMobileMenu();
        });

        // CTA buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('cta-btn') && e.target.getAttribute('data-page')) {
                const page = e.target.getAttribute('data-page');
                this.navigateToPage(page);
            }

            if (e.target.id === 'open-terminal') {
                this.openTerminal();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl + T to open terminal
            if (e.ctrlKey && e.key === 't') {
                e.preventDefault();
                this.toggleTerminal();
            }

            // Escape to close terminal
            if (e.key === 'Escape' && this.terminal.classList.contains('active')) {
                this.closeTerminal();
            }

            // Number keys for quick navigation
            if (e.altKey && e.key >= '1' && e.key <= '7') {
                e.preventDefault();
                const pages = ['home', 'services', 'projects', 'team', 'blog', 'contact', 'login'];
                const pageIndex = parseInt(e.key) - 1;
                if (pages[pageIndex]) {
                    this.navigateToPage(pages[pageIndex]);
                }
            }
        });

        // Smooth scrolling for anchor links
        document.addEventListener('click', (e) => {
            if (e.target.tagName === 'A' && e.target.getAttribute('href')?.startsWith('#')) {
                e.preventDefault();
                const targetId = e.target.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                }
            }
        });

        // Handle browser back/forward buttons
        window.addEventListener('popstate', (e) => {
            const page = e.state?.page || 'home';
            this.navigateToPage(page, false);
        });

        // Control panel buttons
        document.getElementById('fullscreen-btn')?.addEventListener('click', () => {
            this.toggleFullscreen();
        });
    }

    showLoadingScreen() {
        this.loadingScreen.style.display = 'flex';

        // Animate loading progress
        const progressBar = document.getElementById('loading-progress');
        let progress = 0;

        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
                setTimeout(() => {
                    this.hideLoadingScreen();
                }, 500);
            }
            progressBar.style.width = `${progress}%`;
        }, 200);
    }

    hideLoadingScreen() {
        this.loadingScreen.classList.add('hidden');
        this.showNavigation();

        setTimeout(() => {
            this.loadingScreen.style.display = 'none';
            this.startIntroAnimation();
        }, 1000);
    }

    showNavigation() {
        this.mainNav.classList.add('visible');
    }

    preloadAssets() {
        // Preload critical assets
        const criticalAssets = [
            'https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
        ];

        criticalAssets.forEach(asset => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = asset;
            link.as = 'style';
            document.head.appendChild(link);
        });
    }

    startIntroAnimation() {
        // Start the CGG logo animation
        const cggAnimation = window['cggAnimation'];
        if (cggAnimation && typeof cggAnimation.triggerAnimation === 'function') {
            cggAnimation.triggerAnimation();
        }
    }

    navigateToPage(page, updateHistory = true) {
        if (this.isAnimating || page === this.currentPage) return;

        this.isAnimating = true;

        // Update navigation
        this.updateActiveNavLink(page);

        // Hide current page
        const currentPageElement = document.getElementById(`page-${this.currentPage}`);
        if (currentPageElement) {
            currentPageElement.classList.remove('active');
        }

        // Show new page with animation
        setTimeout(() => {
            const newPageElement = document.getElementById(`page-${page}`);
            if (newPageElement) {
                newPageElement.classList.add('active');

                // Trigger page-specific animations
                this.triggerPageAnimations(page);

                // Update browser history
                if (updateHistory) {
                    history.pushState({ page }, '', `#${page}`);
                }

                this.currentPage = page;
                this.isAnimating = false;

                // Close mobile menu if open
                this.closeMobileMenu();

                // Scroll to top
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        }, 300);
    }

    updateActiveNavLink(page) {
        this.navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('data-page') === page) {
                link.classList.add('active');
            }
        });
    }

    triggerPageAnimations(page) {
        const pageElement = document.getElementById(`page-${page}`);
        if (!pageElement) return;

        // Add entrance animation class
        pageElement.classList.add('page-enter');

        // Animate cards/elements with stagger effect
        const animatedElements = pageElement.querySelectorAll('.service-card, .project-card, .team-card, .blog-card, .stat-card');
        animatedElements.forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(30px)';

            setTimeout(() => {
                element.style.transition = 'all 0.6s ease';
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, index * 100 + 200);
        });

        // Remove animation class after completion
        setTimeout(() => {
            pageElement.classList.remove('page-enter');
        }, 1000);
    }

    toggleMobileMenu() {
        this.navMenu.classList.toggle('active');
        this.navToggle.classList.toggle('active');
    }

    closeMobileMenu() {
        this.navMenu.classList.remove('active');
        this.navToggle.classList.remove('active');
    }

    toggleTerminal() {
        if (this.terminal.classList.contains('active')) {
            this.closeTerminal();
        } else {
            this.openTerminal();
        }
    }

    openTerminal() {
        if (window.terminal) {
            window.terminal.openTerminal();
        }
    }

    closeTerminal() {
        if (window.terminal) {
            window.terminal.closeTerminal();
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log('Error attempting to enable fullscreen:', err);
            });
        } else {
            document.exitFullscreen();
        }
    }

    // Utility methods
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Remove after delay
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Page-specific functionality
    initContactForm() {
        const contactForm = document.querySelector('.contact-form');
        if (contactForm) {
            contactForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleContactSubmission(contactForm);
            });
        }
    }

    handleContactSubmission(form) {
        const formData = new FormData(form);
        // In a real application, you would send this data to a server
        console.log('Contact form data:', Object.fromEntries(formData));

        // Simulate form submission
        this.showNotification('Message sent successfully! We\'ll get back to you soon.', 'success');
        form.reset();
    }

    // Initialize page-specific features
    initPageFeatures() {
        // Contact form
        this.initContactForm();

        // Project cards hover effects
        const projectCards = document.querySelectorAll('.project-card');
        projectCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Service cards interactive effects
        const serviceCards = document.querySelectorAll('.service-card');
        serviceCards.forEach(card => {
            card.addEventListener('click', () => {
                card.classList.toggle('expanded');
            });
        });

        // Blog card read more functionality
        const blogLinks = document.querySelectorAll('.blog-link');
        blogLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.showNotification('Blog post will open in a new window', 'info');
            });
        });
    }
}

// Initialize navigation when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.navigationManager = new NavigationManager();

    // Initialize page features after a short delay
    setTimeout(() => {
        window.navigationManager.initPageFeatures();
    }, 1000);
});

// Add notification styles dynamically
const notificationStyles = `
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid #00ff41;
    border-radius: 8px;
    padding: 15px 20px;
    color: #fff;
    font-family: 'Orbitron', monospace;
    z-index: 10000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-success {
    border-color: #50fa7b;
}

.notification-error {
    border-color: #ff5555;
}

.notification-info {
    border-color: #8be9fd;
}
`;

// Inject notification styles
const styleSheet = document.createElement('style');
styleSheet.textContent = notificationStyles;
document.head.appendChild(styleSheet);
