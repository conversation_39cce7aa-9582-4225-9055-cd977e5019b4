// Admin Dashboard Management System
class AdminManager {
    constructor() {
        this.isLoggedIn = false;
        this.currentUser = null;
        this.currentTab = 'services';
        this.editingItem = null;

        // Admin credentials (في التطبيق الحقيقي، يجب أن تكون مشفرة ومحفوظة بشكل آمن)
        this.adminCredentials = {
            username: 'Mossef1',
            password: 'CGG@2024!Secure'
        };

        // Data storage (في التطبيق الحقيقي، يجب أن تكون في قاعدة بيانات)
        this.data = {
            services: [
                {
                    id: 1,
                    title: 'Threat Detection',
                    description: 'Advanced AI-powered threat detection and prevention systems',
                    icon: 'fas fa-shield-virus',
                    features: ['Real-time monitoring', 'Behavioral analysis', 'Zero-day protection']
                },
                {
                    id: 2,
                    title: 'Data Encryption',
                    description: 'Military-grade encryption for sensitive data protection',
                    icon: 'fas fa-lock',
                    features: ['AES-256 encryption', 'Key management', 'Secure transmission']
                },
                {
                    id: 3,
                    title: 'Network Security',
                    description: 'Comprehensive network protection and monitoring',
                    icon: 'fas fa-network-wired',
                    features: ['Firewall management', 'Intrusion detection', 'VPN solutions']
                },
                {
                    id: 4,
                    title: 'Identity Management',
                    description: 'Secure identity and access management solutions',
                    icon: 'fas fa-user-shield',
                    features: ['Multi-factor authentication', 'Single sign-on', 'Privilege management']
                }
            ],
            projects: [
                {
                    id: 1,
                    title: 'SecureBank Platform',
                    description: 'Complete cybersecurity overhaul for major banking institution',
                    tags: ['Banking', 'Encryption', 'Compliance'],
                    status: 'Completed',
                    image: 'https://via.placeholder.com/400x250/00ff41/000000?text=SecureBank',
                    link: 'https://github.com/cgg/securebank-platform',
                    linkType: 'demo'
                },
                {
                    id: 2,
                    title: 'HealthGuard System',
                    description: 'HIPAA-compliant security solution for healthcare networks',
                    tags: ['Healthcare', 'HIPAA', 'Privacy'],
                    status: 'In Progress',
                    image: 'https://via.placeholder.com/400x250/00ffff/000000?text=HealthGuard',
                    link: 'https://github.com/cgg/healthguard-system',
                    linkType: 'download'
                },
                {
                    id: 3,
                    title: 'EduSecure Network',
                    description: 'Educational institution network security implementation',
                    tags: ['Education', 'Network', 'Monitoring'],
                    status: 'Completed',
                    image: 'https://via.placeholder.com/400x250/ff00ff/000000?text=EduSecure',
                    link: 'https://demo.cgg.com/edusecure',
                    linkType: 'demo'
                }
            ],
            team: [
                {
                    id: 1,
                    name: 'Ahmed Al-Rashid',
                    role: 'Chief Security Officer',
                    bio: '15+ years in cybersecurity with expertise in threat intelligence',
                    avatar: 'https://via.placeholder.com/150/00ff41/000000?text=AR',
                    social: {
                        linkedin: 'https://linkedin.com/in/ahmed-rashid',
                        twitter: 'https://twitter.com/ahmed_rashid',
                        github: 'https://github.com/ahmed-rashid'
                    }
                },
                {
                    id: 2,
                    name: 'Sarah Johnson',
                    role: 'Lead Security Engineer',
                    bio: 'Specialist in network security and penetration testing',
                    avatar: 'https://via.placeholder.com/150/00ffff/000000?text=SJ',
                    social: {
                        linkedin: 'https://linkedin.com/in/sarah-johnson',
                        twitter: 'https://twitter.com/sarah_johnson',
                        github: 'https://github.com/sarah-johnson'
                    }
                },
                {
                    id: 3,
                    name: 'محمد العلي',
                    role: 'Cryptography Specialist',
                    bio: 'Expert in encryption algorithms and secure communications',
                    avatar: 'https://via.placeholder.com/150/ff00ff/000000?text=MA',
                    social: {
                        linkedin: 'https://linkedin.com/in/mohammed-ali',
                        twitter: 'https://twitter.com/mohammed_ali',
                        github: 'https://github.com/mohammed-ali'
                    }
                }
            ],
            homeTerminal: {
                commands: [
                    {
                        command: "whoami",
                        response: "CGG Security Group - Elite Cybersecurity Team",
                        type: "info"
                    },
                    {
                        command: "ls -la /security",
                        response: [
                            "drwxr-xr-x  5 <USER> <GROUP>  4096 Dec 15 2024 threat-detection/",
                            "drwxr-xr-x  3 <USER> <GROUP>  4096 Dec 15 2024 encryption/",
                            "drwxr-xr-x  4 <USER> <GROUP>  4096 Dec 15 2024 network-security/",
                            "drwxr-xr-x  2 <USER> <GROUP>  4096 Dec 15 2024 identity-management/"
                        ],
                        type: "success"
                    },
                    {
                        command: "cat /etc/cgg-motto",
                        response: "🛡️ Securing the Digital Future, One Algorithm at a Time",
                        type: "highlight"
                    },
                    {
                        command: "ps aux | grep security",
                        response: [
                            "root     1337  0.1  2.1 AI-ThreatDetector  running",
                            "root     2048  0.2  1.8 QuantumEncryption  active",
                            "root     4096  0.1  1.5 NetworkShield     monitoring"
                        ],
                        type: "success"
                    },
                    {
                        command: "uptime",
                        response: "System uptime: 99.9% | Threats blocked: 1,337,420 | Clients protected: 50+",
                        type: "info"
                    },
                    {
                        command: "echo $MISSION",
                        response: "Protecting digital assets with cutting-edge cybersecurity solutions",
                        type: "highlight"
                    }
                ],
                interactiveCommands: [
                    { cmd: "scan", desc: "Security Scan", icon: "🔍" },
                    { cmd: "encrypt", desc: "Encrypt Data", icon: "🔐" },
                    { cmd: "firewall", desc: "Firewall Status", icon: "🛡️" },
                    { cmd: "services", desc: "View Services", icon: "⚙️" },
                    { cmd: "team", desc: "Meet Team", icon: "👥" },
                    { cmd: "help", desc: "Show Help", icon: "❓" }
                ]
            },
            blog: [
                {
                    id: 1,
                    title: 'The Rise of AI-Powered Cyber Attacks',
                    description: 'How artificial intelligence is being weaponized by cybercriminals and how to defend against it...',
                    category: 'Threat Intelligence',
                    date: 'Dec 15, 2024',
                    author: 'Ahmed Al-Rashid',
                    image: 'https://via.placeholder.com/400x250/00ff41/000000?text=AI+Attacks',
                    link: 'https://blog.cgg.com/ai-powered-cyber-attacks'
                },
                {
                    id: 2,
                    title: 'Zero Trust Architecture Implementation',
                    description: 'A comprehensive guide to implementing zero trust security in modern enterprises...',
                    category: 'Best Practices',
                    date: 'Dec 10, 2024',
                    author: 'Sarah Johnson',
                    image: 'https://via.placeholder.com/400x250/00ffff/000000?text=Zero+Trust',
                    link: 'https://blog.cgg.com/zero-trust-architecture'
                },
                {
                    id: 3,
                    title: 'GDPR Compliance in 2024',
                    description: 'Updated guidelines and best practices for maintaining GDPR compliance...',
                    category: 'Compliance',
                    date: 'Dec 5, 2024',
                    author: 'محمد العلي',
                    image: 'https://via.placeholder.com/400x250/ff00ff/000000?text=GDPR+2024',
                    link: 'https://blog.cgg.com/gdpr-compliance-2024'
                }
            ]
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkLoginStatus();
    }

    setupEventListeners() {
        // Login form
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // Logout button
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.handleLogout();
            });
        }

        // Tab buttons
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const tab = btn.getAttribute('data-tab');
                this.switchTab(tab);
            });
        });

        // Add buttons
        document.addEventListener('click', (e) => {
            if (e.target.id === 'add-service-btn') this.openAddModal('service');
            if (e.target.id === 'add-project-btn') this.openAddModal('project');
            if (e.target.id === 'add-team-btn') this.openAddModal('team');
            if (e.target.id === 'add-blog-btn') this.openAddModal('blog');
            if (e.target.id === 'add-terminal-text-btn') this.openAddTerminalTextModal();
        });

        // Terminal settings
        const saveSettingsBtn = document.getElementById('save-terminal-settings');
        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', () => this.saveTerminalSettings());
        }

        // Modal events
        const modal = document.getElementById('edit-modal');
        const modalClose = document.getElementById('modal-close');
        const cancelBtn = document.getElementById('cancel-btn');
        const modalForm = document.getElementById('modal-form');

        if (modalClose) {
            modalClose.addEventListener('click', () => this.closeModal());
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.closeModal());
        }

        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) this.closeModal();
            });
        }

        if (modalForm) {
            modalForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSave();
            });
        }
    }



    checkLoginStatus() {
        // Check if user is remembered
        const rememberedUser = localStorage.getItem('cgg_admin_user');
        const loginTime = localStorage.getItem('cgg_admin_login_time');

        if (rememberedUser && loginTime) {
            const now = new Date().getTime();
            const loginTimestamp = parseInt(loginTime);
            const hoursPassed = (now - loginTimestamp) / (1000 * 60 * 60);

            if (hoursPassed < 24) {
                this.isLoggedIn = true;
                this.currentUser = rememberedUser;
                this.showDashboard();
                return;
            } else {
                // Login expired
                localStorage.removeItem('cgg_admin_user');
                localStorage.removeItem('cgg_admin_login_time');
            }
        }
    }

    handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('remember-me').checked;

        // Simulate login process with animation
        const loginBtn = document.querySelector('.login-btn');
        const originalText = loginBtn.innerHTML;

        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Authenticating...';
        loginBtn.disabled = true;

        setTimeout(() => {
            if (username === this.adminCredentials.username && password === this.adminCredentials.password) {
                this.isLoggedIn = true;
                this.currentUser = username;

                if (rememberMe) {
                    localStorage.setItem('cgg_admin_user', username);
                    localStorage.setItem('cgg_admin_login_time', new Date().getTime().toString());
                }

                this.showSuccessMessage('Login successful! Welcome back, ' + username);
                setTimeout(() => {
                    this.showDashboard();
                }, 1500);
            } else {
                this.showErrorMessage('Invalid credentials. Please try again.');
                loginBtn.innerHTML = originalText;
                loginBtn.disabled = false;
            }
        }, 2000);
    }

    handleLogout() {
        this.isLoggedIn = false;
        this.currentUser = null;

        // Clear remembered login
        localStorage.removeItem('cgg_admin_user');
        localStorage.removeItem('cgg_admin_login_time');

        // Navigate back to login
        if (window.navigationManager) {
            window.navigationManager.navigateToPage('login');
        }

        this.showSuccessMessage('Logged out successfully');
    }

    showDashboard() {
        // Hide login page and show dashboard
        const loginPage = document.getElementById('page-login');
        const dashboardPage = document.getElementById('page-dashboard');

        if (loginPage) loginPage.style.display = 'none';
        if (dashboardPage) {
            dashboardPage.style.display = 'block';
            dashboardPage.classList.add('active');
        }

        // Update navigation
        if (window.navigationManager) {
            window.navigationManager.currentPage = 'dashboard';
            window.navigationManager.updateActiveNavLink('dashboard');
        }

        // Load dashboard data
        this.loadDashboardData();
    }

    loadDashboardData() {
        this.updateStats();
        this.loadTabContent(this.currentTab);
    }

    updateStats() {
        // Update dashboard statistics
        const stats = document.querySelectorAll('.admin-stat .stat-number');
        if (stats.length >= 4) {
            stats[0].textContent = this.data.services.length;
            stats[1].textContent = this.data.projects.length;
            stats[2].textContent = this.data.team.length;
            stats[3].textContent = this.data.blog.length;
        }
    }

    switchTab(tab) {
        this.currentTab = tab;

        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tab}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`tab-${tab}`).classList.add('active');

        // Load content for the tab
        this.loadTabContent(tab);
    }

    loadTabContent(tab) {
        if (tab === 'home-terminal') {
            this.loadHomeTerminalContent();
            return;
        }

        const container = document.getElementById(`${tab}-admin-grid`);
        if (!container) return;

        container.innerHTML = '';

        this.data[tab].forEach(item => {
            const itemElement = this.createItemElement(tab, item);
            container.appendChild(itemElement);
        });
    }

    loadHomeTerminalContent() {
        this.loadTerminalSettings();
        this.loadTerminalTexts();
    }

    loadTerminalSettings() {
        const settings = window.homeTerminal ? window.homeTerminal.getSettings() : {
            typingSpeed: 100,
            pauseDuration: 2000,
            soundEnabled: true,
            autoLoop: true
        };

        document.getElementById('typing-speed').value = settings.typingSpeed;
        document.getElementById('pause-duration').value = settings.pauseDuration;
        document.getElementById('sound-enabled').checked = settings.soundEnabled;
        document.getElementById('auto-loop').checked = settings.autoLoop;
    }

    loadTerminalTexts() {
        const container = document.getElementById('terminal-texts-admin-grid');
        if (!container) return;

        container.innerHTML = '';

        const terminalTexts = window.homeTerminal ? window.homeTerminal.getTerminalTexts() : [];

        terminalTexts.forEach(textData => {
            const textElement = this.createTerminalTextElement(textData);
            container.appendChild(textElement);
        });
    }

    createTerminalTextElement(textData) {
        const div = document.createElement('div');
        div.className = 'terminal-text-card';
        div.setAttribute('data-id', textData.id);

        const outputPreview = textData.output ?
            textData.output.slice(0, 2).map(o => o.text).join('<br>') +
            (textData.output.length > 2 ? '<br>...' : '') : '';

        div.innerHTML = `
            <h4>
                <i class="fas fa-terminal"></i>
                Command Text #${textData.id}
            </h4>
            <div class="text-content">${textData.content}</div>
            <div class="text-meta">
                <span class="text-type">${textData.type}</span>
                <span class="text-delay">${textData.delay}ms delay</span>
            </div>
            ${outputPreview ? `<div class="text-content">${outputPreview}</div>` : ''}
            <div class="card-actions">
                <button class="edit-text-btn" onclick="adminManager.editTerminalText(${textData.id})">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button class="delete-text-btn" onclick="adminManager.deleteTerminalText(${textData.id})">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        `;

        return div;
    }

    saveTerminalSettings() {
        const settings = {
            typingSpeed: parseInt(document.getElementById('typing-speed').value),
            pauseDuration: parseInt(document.getElementById('pause-duration').value),
            soundEnabled: document.getElementById('sound-enabled').checked,
            autoLoop: document.getElementById('auto-loop').checked
        };

        if (window.homeTerminal) {
            window.homeTerminal.updateSettings(settings);
        }

        this.showSuccessMessage('Terminal settings saved successfully!');
    }

    openAddTerminalTextModal() {
        this.openTerminalTextModal('add', null);
    }

    editTerminalText(id) {
        const terminalTexts = window.homeTerminal ? window.homeTerminal.getTerminalTexts() : [];
        const textData = terminalTexts.find(t => t.id === id);
        if (textData) {
            this.openTerminalTextModal('edit', textData);
        }
    }

    deleteTerminalText(id) {
        if (confirm('Are you sure you want to delete this terminal text?')) {
            if (window.homeTerminal) {
                window.homeTerminal.deleteTerminalText(id);
            }
            this.loadTerminalTexts();
            this.showSuccessMessage('Terminal text deleted successfully!');
        }
    }

    openTerminalTextModal(mode, textData = null) {
        const modal = document.getElementById('edit-modal');
        const modalTitle = document.getElementById('modal-title');
        const modalFields = document.getElementById('modal-fields');

        modalTitle.textContent = mode === 'add' ? 'Add Terminal Text' : 'Edit Terminal Text';

        const outputFields = textData && textData.output ?
            textData.output.map((o, i) => `
                <div class="form-group">
                    <label for="output-${i}-type">Output ${i + 1} Type</label>
                    <select id="output-${i}-type" required>
                        <option value="success" ${o.type === 'success' ? 'selected' : ''}>Success</option>
                        <option value="info" ${o.type === 'info' ? 'selected' : ''}>Info</option>
                        <option value="warning" ${o.type === 'warning' ? 'selected' : ''}>Warning</option>
                        <option value="error" ${o.type === 'error' ? 'selected' : ''}>Error</option>
                        <option value="output" ${o.type === 'output' ? 'selected' : ''}>Output</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="output-${i}-text">Output ${i + 1} Text</label>
                    <input type="text" id="output-${i}-text" value="${o.text}" required>
                </div>
            `).join('') : '';

        modalFields.innerHTML = `
            <div class="form-group">
                <label for="terminal-text-type">Type</label>
                <select id="terminal-text-type" required>
                    <option value="command" ${textData && textData.type === 'command' ? 'selected' : ''}>Command</option>
                    <option value="info" ${textData && textData.type === 'info' ? 'selected' : ''}>Info</option>
                    <option value="system" ${textData && textData.type === 'system' ? 'selected' : ''}>System</option>
                </select>
            </div>
            <div class="form-group">
                <label for="terminal-text-content">Command/Text Content</label>
                <textarea id="terminal-text-content" rows="3" required>${textData ? textData.content : ''}</textarea>
            </div>
            <div class="form-group">
                <label for="terminal-text-delay">Delay (milliseconds)</label>
                <input type="number" id="terminal-text-delay" value="${textData ? textData.delay : 3000}" min="1000" max="10000" required>
            </div>
            <div class="form-group">
                <label>Output Lines (optional)</label>
                <div id="output-lines">
                    ${outputFields}
                </div>
                <button type="button" onclick="adminManager.addOutputLine()" class="add-output-btn">
                    <i class="fas fa-plus"></i> Add Output Line
                </button>
            </div>
        `;

        this.editingItem = textData;
        this.editingType = 'terminal-text';
        modal.style.display = 'flex';
    }

    addOutputLine() {
        const container = document.getElementById('output-lines');
        const index = container.children.length / 2; // Each output has 2 fields

        const outputHTML = `
            <div class="form-group">
                <label for="output-${index}-type">Output ${index + 1} Type</label>
                <select id="output-${index}-type" required>
                    <option value="success">Success</option>
                    <option value="info">Info</option>
                    <option value="warning">Warning</option>
                    <option value="error">Error</option>
                    <option value="output">Output</option>
                </select>
            </div>
            <div class="form-group">
                <label for="output-${index}-text">Output ${index + 1} Text</label>
                <input type="text" id="output-${index}-text" required>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', outputHTML);
    }

    loadTerminalContent() {
        this.loadTerminalCommands();
        this.loadInteractiveButtons();
    }

    loadTerminalCommands() {
        const container = document.getElementById('terminal-commands-list');
        if (!container) return;

        container.innerHTML = '';

        this.data.homeTerminal.commands.forEach((command, index) => {
            const commandElement = this.createTerminalCommandElement(command, index);
            container.appendChild(commandElement);
        });
    }

    loadInteractiveButtons() {
        const container = document.getElementById('interactive-buttons-list');
        if (!container) return;

        container.innerHTML = '';

        this.data.homeTerminal.interactiveCommands.forEach((button, index) => {
            const buttonElement = this.createInteractiveButtonElement(button, index);
            container.appendChild(buttonElement);
        });
    }

    createTerminalCommandElement(command, index) {
        const div = document.createElement('div');
        div.className = 'terminal-command-item';

        const responseText = Array.isArray(command.response)
            ? command.response.join('<br>')
            : command.response;

        div.innerHTML = `
            <div class="command-type ${command.type}">${command.type}</div>
            <div class="command-preview">
                <div>$ ${command.command}</div>
                <div class="command-response">${responseText}</div>
            </div>
            <div class="item-actions">
                <button class="edit-item-btn edit-terminal-cmd" data-index="${index}">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button class="delete-item-btn delete-terminal-cmd" data-index="${index}">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        `;

        return div;
    }

    createInteractiveButtonElement(button, index) {
        const div = document.createElement('div');
        div.className = 'interactive-button-item';

        div.innerHTML = `
            <div class="button-preview">
                <span class="button-icon">${button.icon}</span>
                <span class="button-text">${button.desc}</span>
                <span class="button-command">${button.cmd}</span>
            </div>
            <div class="item-actions">
                <button class="edit-item-btn edit-interactive-btn" data-index="${index}">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button class="delete-item-btn delete-interactive-btn" data-index="${index}">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        `;

        return div;
    }

    createItemElement(type, item) {
        const div = document.createElement('div');
        div.className = 'admin-item';
        div.setAttribute('data-id', item.id);

        let content = '';

        switch (type) {
            case 'services':
                content = `
                    <h4>${item.title}</h4>
                    <p>${item.description}</p>
                    <div class="service-features">
                        ${item.features.map(feature => `<span class="tag">${feature}</span>`).join('')}
                    </div>
                `;
                break;
            case 'projects':
                content = `
                    <div class="admin-item-image">
                        <img src="${item.image}" alt="${item.title}" style="width: 100%; height: 150px; object-fit: cover; border-radius: 8px; margin-bottom: 15px;">
                    </div>
                    <h4>${item.title}</h4>
                    <p>${item.description}</p>
                    <div class="project-tags">
                        ${item.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                    <div class="project-status">Status: <strong>${item.status}</strong></div>
                    <div class="project-link">
                        <a href="${item.link}" target="_blank" class="project-link-btn ${item.linkType}">
                            <i class="fas fa-${item.linkType === 'demo' ? 'external-link-alt' : 'download'}"></i>
                            ${item.linkType === 'demo' ? 'View Demo' : 'Download'}
                        </a>
                    </div>
                `;
                break;
            case 'team':
                content = `
                    <div class="admin-item-image">
                        <img src="${item.avatar}" alt="${item.name}" style="width: 100px; height: 100px; object-fit: cover; border-radius: 50%; margin: 0 auto 15px; display: block;">
                    </div>
                    <h4>${item.name}</h4>
                    <p class="team-role">${item.role}</p>
                    <p>${item.bio}</p>
                    <div class="team-social-admin">
                        <a href="${item.social.linkedin}" target="_blank" class="social-link linkedin">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="${item.social.twitter}" target="_blank" class="social-link twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="${item.social.github}" target="_blank" class="social-link github">
                            <i class="fab fa-github"></i>
                        </a>
                    </div>
                `;
                break;
            case 'blog':
                content = `
                    <div class="admin-item-image">
                        <img src="${item.image}" alt="${item.title}" style="width: 100%; height: 150px; object-fit: cover; border-radius: 8px; margin-bottom: 15px;">
                    </div>
                    <h4>${item.title}</h4>
                    <p>${item.description}</p>
                    <div class="blog-meta">
                        <span class="blog-category">${item.category}</span>
                        <span class="blog-date">${item.date}</span>
                    </div>
                    <div class="blog-author">By: ${item.author}</div>
                    <div class="blog-link">
                        <a href="${item.link}" target="_blank" class="blog-link-btn">
                            <i class="fas fa-external-link-alt"></i>
                            Read Full Article
                        </a>
                    </div>
                `;
                break;
        }

        content += `
            <div class="admin-item-actions">
                <button class="edit-btn" onclick="window.adminManager.editItem('${type}', ${item.id})">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button class="delete-btn" onclick="window.adminManager.deleteItem('${type}', ${item.id})">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        `;

        div.innerHTML = content;
        return div;
    }

    openAddModal(type) {
        this.editingItem = null;
        this.openModal(type, null);
    }

    editItem(type, id) {
        const item = this.data[type].find(item => item.id === id);
        if (item) {
            this.editingItem = { type, id };
            this.openModal(type, item);
        }
    }

    deleteItem(type, id) {
        if (confirm('Are you sure you want to delete this item?')) {
            this.data[type] = this.data[type].filter(item => item.id !== id);
            this.loadTabContent(type);
            this.updateStats();
            this.updatePublicPages();
            this.showSuccessMessage('Item deleted successfully');
        }
    }

    openModal(type, item) {
        const modal = document.getElementById('edit-modal');
        const modalTitle = document.getElementById('modal-title');
        const modalFields = document.getElementById('modal-fields');

        modalTitle.textContent = item ? `Edit ${type.slice(0, -1)}` : `Add New ${type.slice(0, -1)}`;

        // Generate form fields based on type
        modalFields.innerHTML = this.generateFormFields(type, item);

        modal.classList.add('active');
    }

    generateFormFields(type, item) {
        let fields = '';

        switch (type) {
            case 'services':
                fields = `
                    <div class="form-group">
                        <label for="title">Service Title</label>
                        <input type="text" id="title" name="title" value="${item?.title || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" required>${item?.description || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label for="icon">Icon Class</label>
                        <input type="text" id="icon" name="icon" value="${item?.icon || 'fas fa-shield-alt'}" required>
                    </div>
                    <div class="form-group">
                        <label for="features">Features (comma-separated)</label>
                        <textarea id="features" name="features" required>${item?.features?.join(', ') || ''}</textarea>
                    </div>
                `;
                break;
            case 'projects':
                fields = `
                    <div class="form-group">
                        <label for="title">Project Title</label>
                        <input type="text" id="title" name="title" value="${item?.title || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" required>${item?.description || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label for="image">Project Image URL</label>
                        <input type="url" id="image" name="image" value="${item?.image || ''}" placeholder="https://example.com/image.jpg" required>
                    </div>
                    <div class="form-group">
                        <label for="tags">Tags (comma-separated)</label>
                        <input type="text" id="tags" name="tags" value="${item?.tags?.join(', ') || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="status" name="status" required>
                            <option value="In Progress" ${item?.status === 'In Progress' ? 'selected' : ''}>In Progress</option>
                            <option value="Completed" ${item?.status === 'Completed' ? 'selected' : ''}>Completed</option>
                            <option value="On Hold" ${item?.status === 'On Hold' ? 'selected' : ''}>On Hold</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="link">Project Link</label>
                        <input type="url" id="link" name="link" value="${item?.link || ''}" placeholder="https://github.com/project or https://demo.com" required>
                    </div>
                    <div class="form-group">
                        <label for="linkType">Link Type</label>
                        <select id="linkType" name="linkType" required>
                            <option value="demo" ${item?.linkType === 'demo' ? 'selected' : ''}>Demo/Preview</option>
                            <option value="download" ${item?.linkType === 'download' ? 'selected' : ''}>Download/Repository</option>
                        </select>
                    </div>
                `;
                break;
            case 'team':
                fields = `
                    <div class="form-group">
                        <label for="name">Name</label>
                        <input type="text" id="name" name="name" value="${item?.name || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="role">Role</label>
                        <input type="text" id="role" name="role" value="${item?.role || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="bio">Bio</label>
                        <textarea id="bio" name="bio" required>${item?.bio || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label for="avatar">Avatar Image URL</label>
                        <input type="url" id="avatar" name="avatar" value="${item?.avatar || ''}" placeholder="https://example.com/avatar.jpg" required>
                    </div>
                    <div class="form-group">
                        <label for="linkedin">LinkedIn Profile</label>
                        <input type="url" id="linkedin" name="linkedin" value="${item?.social?.linkedin || ''}" placeholder="https://linkedin.com/in/username">
                    </div>
                    <div class="form-group">
                        <label for="twitter">Twitter Profile</label>
                        <input type="url" id="twitter" name="twitter" value="${item?.social?.twitter || ''}" placeholder="https://twitter.com/username">
                    </div>
                    <div class="form-group">
                        <label for="github">GitHub Profile</label>
                        <input type="url" id="github" name="github" value="${item?.social?.github || ''}" placeholder="https://github.com/username">
                    </div>
                `;
                break;
            case 'blog':
                fields = `
                    <div class="form-group">
                        <label for="title">Post Title</label>
                        <input type="text" id="title" name="title" value="${item?.title || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" required>${item?.description || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label for="image">Featured Image URL</label>
                        <input type="url" id="image" name="image" value="${item?.image || ''}" placeholder="https://example.com/image.jpg" required>
                    </div>
                    <div class="form-group">
                        <label for="category">Category</label>
                        <input type="text" id="category" name="category" value="${item?.category || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="author">Author</label>
                        <input type="text" id="author" name="author" value="${item?.author || this.currentUser}" required>
                    </div>
                    <div class="form-group">
                        <label for="link">Article Link</label>
                        <input type="url" id="link" name="link" value="${item?.link || ''}" placeholder="https://blog.cgg.com/article-slug" required>
                    </div>
                `;
                break;
        }

        return fields;
    }

    closeModal() {
        const modal = document.getElementById('edit-modal');
        modal.style.display = 'none';
        this.editingItem = null;
        this.editingType = null;
    }

    handleSave() {
        // Handle terminal text saving
        if (this.editingType === 'terminal-text') {
            this.saveTerminalText();
            return;
        }

        const formData = new FormData(document.getElementById('modal-form'));
        const data = Object.fromEntries(formData);

        if (this.editingItem) {
            // Edit existing item
            const { type, id } = this.editingItem;
            const itemIndex = this.data[type].findIndex(item => item.id === id);

            if (itemIndex !== -1) {
                this.data[type][itemIndex] = { ...this.data[type][itemIndex], ...this.processFormData(type, data) };
                this.showSuccessMessage('Item updated successfully');
            }
        } else {
            // Add new item
            const type = this.currentTab;
            const newId = Math.max(...this.data[type].map(item => item.id)) + 1;
            const newItem = { id: newId, ...this.processFormData(type, data) };

            this.data[type].push(newItem);
            this.showSuccessMessage('Item added successfully');
        }

        this.loadTabContent(this.currentTab);
        this.updateStats();
        this.updatePublicPages();
        this.closeModal();
    }

    saveTerminalText() {
        const type = document.getElementById('terminal-text-type').value;
        const content = document.getElementById('terminal-text-content').value;
        const delay = parseInt(document.getElementById('terminal-text-delay').value);

        // Collect output lines
        const output = [];
        const outputContainer = document.getElementById('output-lines');
        const outputFields = outputContainer.querySelectorAll('[id^="output-"]');

        for (let i = 0; i < outputFields.length; i += 2) {
            const typeField = outputFields[i];
            const textField = outputFields[i + 1];

            if (typeField && textField && textField.value.trim()) {
                output.push({
                    type: typeField.value,
                    text: textField.value.trim()
                });
            }
        }

        const terminalTextData = {
            type,
            content,
            delay,
            output: output.length > 0 ? output : undefined
        };

        if (this.editingItem) {
            // Edit existing
            if (window.homeTerminal) {
                window.homeTerminal.updateTerminalText(this.editingItem.id, terminalTextData);
            }
            this.showSuccessMessage('Terminal text updated successfully!');
        } else {
            // Add new
            if (window.homeTerminal) {
                window.homeTerminal.addTerminalText(terminalTextData);
            }
            this.showSuccessMessage('Terminal text added successfully!');
        }

        this.loadTerminalTexts();
        this.closeModal();
    }

    processFormData(type, data) {
        const processed = { ...data };

        // Process specific fields based on type
        if (type === 'services' && data.features) {
            processed.features = data.features.split(',').map(f => f.trim());
        }

        if (type === 'projects' && data.tags) {
            processed.tags = data.tags.split(',').map(t => t.trim());
        }

        if (type === 'team') {
            // Process social media links
            processed.social = {
                linkedin: data.linkedin || '',
                twitter: data.twitter || '',
                github: data.github || ''
            };
            // Remove individual social fields from processed data
            delete processed.linkedin;
            delete processed.twitter;
            delete processed.github;
        }

        if (type === 'blog' && !data.date) {
            processed.date = new Date().toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        return processed;
    }

    updatePublicPages() {
        // Update the public pages with new data
        if (window.publicDataRenderer) {
            window.publicDataRenderer.updatePublicData();
        }

        // Update live terminal
        if (window.liveTerminal) {
            window.liveTerminal.updateCommands(
                this.data.homeTerminal.commands,
                this.data.homeTerminal.interactiveCommands
            );
        }
    }

    // Terminal Management Methods
    addTerminalCommand() {
        const command = prompt('Enter command:');
        const response = prompt('Enter response:');
        const type = prompt('Enter type (info/success/highlight):') || 'info';

        if (command && response) {
            this.data.homeTerminal.commands.push({
                command,
                response,
                type
            });
            this.loadTerminalCommands();
            this.updatePublicPages();
            this.showSuccessMessage('Terminal command added successfully');
        }
    }

    editTerminalCommand(index) {
        const command = this.data.homeTerminal.commands[index];
        if (!command) return;

        const newCommand = prompt('Edit command:', command.command);
        const newResponse = prompt('Edit response:', Array.isArray(command.response) ? command.response.join('\n') : command.response);
        const newType = prompt('Edit type (info/success/highlight):', command.type);

        if (newCommand && newResponse) {
            this.data.homeTerminal.commands[index] = {
                command: newCommand,
                response: newResponse.includes('\n') ? newResponse.split('\n') : newResponse,
                type: newType || 'info'
            };
            this.loadTerminalCommands();
            this.updatePublicPages();
            this.showSuccessMessage('Terminal command updated successfully');
        }
    }

    deleteTerminalCommand(index) {
        if (confirm('Are you sure you want to delete this command?')) {
            this.data.homeTerminal.commands.splice(index, 1);
            this.loadTerminalCommands();
            this.updatePublicPages();
            this.showSuccessMessage('Terminal command deleted successfully');
        }
    }

    addInteractiveButton() {
        const cmd = prompt('Enter command:');
        const desc = prompt('Enter description:');
        const icon = prompt('Enter icon (emoji):');

        if (cmd && desc && icon) {
            this.data.homeTerminal.interactiveCommands.push({
                cmd,
                desc,
                icon
            });
            this.loadInteractiveButtons();
            this.updatePublicPages();
            this.showSuccessMessage('Interactive button added successfully');
        }
    }

    editInteractiveButton(index) {
        const button = this.data.homeTerminal.interactiveCommands[index];
        if (!button) return;

        const newCmd = prompt('Edit command:', button.cmd);
        const newDesc = prompt('Edit description:', button.desc);
        const newIcon = prompt('Edit icon:', button.icon);

        if (newCmd && newDesc && newIcon) {
            this.data.homeTerminal.interactiveCommands[index] = {
                cmd: newCmd,
                desc: newDesc,
                icon: newIcon
            };
            this.loadInteractiveButtons();
            this.updatePublicPages();
            this.showSuccessMessage('Interactive button updated successfully');
        }
    }

    deleteInteractiveButton(index) {
        if (confirm('Are you sure you want to delete this button?')) {
            this.data.homeTerminal.interactiveCommands.splice(index, 1);
            this.loadInteractiveButtons();
            this.updatePublicPages();
            this.showSuccessMessage('Interactive button deleted successfully');
        }
    }

    showSuccessMessage(message) {
        if (window.navigationManager) {
            window.navigationManager.showNotification(message, 'success');
        }
    }

    showErrorMessage(message) {
        if (window.navigationManager) {
            window.navigationManager.showNotification(message, 'error');
        }
    }
}

// Initialize admin manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adminManager = new AdminManager();
});
