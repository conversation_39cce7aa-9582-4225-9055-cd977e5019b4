// Admin Dashboard Management System
class AdminManager {
    constructor() {
        this.isLoggedIn = false;
        this.currentUser = null;
        this.currentTab = 'services';
        this.editingItem = null;
        
        // Admin credentials (في التطبيق الحقيقي، يجب أن تكون مشفرة ومحفوظة بشكل آمن)
        this.adminCredentials = {
            username: 'Mossef1',
            password: 'CGG@2024!Secure'
        };

        // Data storage (في التطبيق الحقيقي، يجب أن تكون في قاعدة بيانات)
        this.data = {
            services: [
                {
                    id: 1,
                    title: 'Threat Detection',
                    description: 'Advanced AI-powered threat detection and prevention systems',
                    icon: 'fas fa-shield-virus',
                    features: ['Real-time monitoring', 'Behavioral analysis', 'Zero-day protection']
                },
                {
                    id: 2,
                    title: 'Data Encryption',
                    description: 'Military-grade encryption for sensitive data protection',
                    icon: 'fas fa-lock',
                    features: ['AES-256 encryption', 'Key management', 'Secure transmission']
                },
                {
                    id: 3,
                    title: 'Network Security',
                    description: 'Comprehensive network protection and monitoring',
                    icon: 'fas fa-network-wired',
                    features: ['Firewall management', 'Intrusion detection', 'VPN solutions']
                },
                {
                    id: 4,
                    title: 'Identity Management',
                    description: 'Secure identity and access management solutions',
                    icon: 'fas fa-user-shield',
                    features: ['Multi-factor authentication', 'Single sign-on', 'Privilege management']
                }
            ],
            projects: [
                {
                    id: 1,
                    title: 'SecureBank Platform',
                    description: 'Complete cybersecurity overhaul for major banking institution',
                    tags: ['Banking', 'Encryption', 'Compliance'],
                    status: 'Completed'
                },
                {
                    id: 2,
                    title: 'HealthGuard System',
                    description: 'HIPAA-compliant security solution for healthcare networks',
                    tags: ['Healthcare', 'HIPAA', 'Privacy'],
                    status: 'In Progress'
                },
                {
                    id: 3,
                    title: 'EduSecure Network',
                    description: 'Educational institution network security implementation',
                    tags: ['Education', 'Network', 'Monitoring'],
                    status: 'Completed'
                }
            ],
            team: [
                {
                    id: 1,
                    name: 'Ahmed Al-Rashid',
                    role: 'Chief Security Officer',
                    bio: '15+ years in cybersecurity with expertise in threat intelligence',
                    avatar: 'fas fa-user'
                },
                {
                    id: 2,
                    name: 'Sarah Johnson',
                    role: 'Lead Security Engineer',
                    bio: 'Specialist in network security and penetration testing',
                    avatar: 'fas fa-user'
                },
                {
                    id: 3,
                    name: 'محمد العلي',
                    role: 'Cryptography Specialist',
                    bio: 'Expert in encryption algorithms and secure communications',
                    avatar: 'fas fa-user'
                }
            ],
            blog: [
                {
                    id: 1,
                    title: 'The Rise of AI-Powered Cyber Attacks',
                    description: 'How artificial intelligence is being weaponized by cybercriminals and how to defend against it...',
                    category: 'Threat Intelligence',
                    date: 'Dec 15, 2024',
                    author: 'Ahmed Al-Rashid'
                },
                {
                    id: 2,
                    title: 'Zero Trust Architecture Implementation',
                    description: 'A comprehensive guide to implementing zero trust security in modern enterprises...',
                    category: 'Best Practices',
                    date: 'Dec 10, 2024',
                    author: 'Sarah Johnson'
                },
                {
                    id: 3,
                    title: 'GDPR Compliance in 2024',
                    description: 'Updated guidelines and best practices for maintaining GDPR compliance...',
                    category: 'Compliance',
                    date: 'Dec 5, 2024',
                    author: 'محمد العلي'
                }
            ]
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkLoginStatus();
    }

    setupEventListeners() {
        // Login form
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // Logout button
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.handleLogout();
            });
        }

        // Tab buttons
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const tab = btn.getAttribute('data-tab');
                this.switchTab(tab);
            });
        });

        // Add buttons
        document.addEventListener('click', (e) => {
            if (e.target.id === 'add-service-btn') this.openAddModal('service');
            if (e.target.id === 'add-project-btn') this.openAddModal('project');
            if (e.target.id === 'add-team-btn') this.openAddModal('team');
            if (e.target.id === 'add-blog-btn') this.openAddModal('blog');
        });

        // Modal events
        const modal = document.getElementById('edit-modal');
        const modalClose = document.getElementById('modal-close');
        const cancelBtn = document.getElementById('cancel-btn');
        const modalForm = document.getElementById('modal-form');

        if (modalClose) {
            modalClose.addEventListener('click', () => this.closeModal());
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.closeModal());
        }

        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) this.closeModal();
            });
        }

        if (modalForm) {
            modalForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSave();
            });
        }
    }

    checkLoginStatus() {
        // Check if user is remembered
        const rememberedUser = localStorage.getItem('cgg_admin_user');
        const loginTime = localStorage.getItem('cgg_admin_login_time');
        
        if (rememberedUser && loginTime) {
            const now = new Date().getTime();
            const loginTimestamp = parseInt(loginTime);
            const hoursPassed = (now - loginTimestamp) / (1000 * 60 * 60);
            
            if (hoursPassed < 24) {
                this.isLoggedIn = true;
                this.currentUser = rememberedUser;
                this.showDashboard();
                return;
            } else {
                // Login expired
                localStorage.removeItem('cgg_admin_user');
                localStorage.removeItem('cgg_admin_login_time');
            }
        }
    }

    handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('remember-me').checked;

        // Simulate login process with animation
        const loginBtn = document.querySelector('.login-btn');
        const originalText = loginBtn.innerHTML;
        
        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Authenticating...';
        loginBtn.disabled = true;

        setTimeout(() => {
            if (username === this.adminCredentials.username && password === this.adminCredentials.password) {
                this.isLoggedIn = true;
                this.currentUser = username;
                
                if (rememberMe) {
                    localStorage.setItem('cgg_admin_user', username);
                    localStorage.setItem('cgg_admin_login_time', new Date().getTime().toString());
                }
                
                this.showSuccessMessage('Login successful! Welcome back, ' + username);
                setTimeout(() => {
                    this.showDashboard();
                }, 1500);
            } else {
                this.showErrorMessage('Invalid credentials. Please try again.');
                loginBtn.innerHTML = originalText;
                loginBtn.disabled = false;
            }
        }, 2000);
    }

    handleLogout() {
        this.isLoggedIn = false;
        this.currentUser = null;
        
        // Clear remembered login
        localStorage.removeItem('cgg_admin_user');
        localStorage.removeItem('cgg_admin_login_time');
        
        // Navigate back to login
        if (window.navigationManager) {
            window.navigationManager.navigateToPage('login');
        }
        
        this.showSuccessMessage('Logged out successfully');
    }

    showDashboard() {
        // Hide login page and show dashboard
        const loginPage = document.getElementById('page-login');
        const dashboardPage = document.getElementById('page-dashboard');
        
        if (loginPage) loginPage.style.display = 'none';
        if (dashboardPage) {
            dashboardPage.style.display = 'block';
            dashboardPage.classList.add('active');
        }

        // Update navigation
        if (window.navigationManager) {
            window.navigationManager.currentPage = 'dashboard';
            window.navigationManager.updateActiveNavLink('dashboard');
        }

        // Load dashboard data
        this.loadDashboardData();
    }

    loadDashboardData() {
        this.updateStats();
        this.loadTabContent(this.currentTab);
    }

    updateStats() {
        // Update dashboard statistics
        const stats = document.querySelectorAll('.admin-stat .stat-number');
        if (stats.length >= 4) {
            stats[0].textContent = this.data.services.length;
            stats[1].textContent = this.data.projects.length;
            stats[2].textContent = this.data.team.length;
            stats[3].textContent = this.data.blog.length;
        }
    }

    switchTab(tab) {
        this.currentTab = tab;
        
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tab}"]`).classList.add('active');
        
        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`tab-${tab}`).classList.add('active');
        
        // Load content for the tab
        this.loadTabContent(tab);
    }

    loadTabContent(tab) {
        const container = document.getElementById(`${tab}-admin-grid`);
        if (!container) return;

        container.innerHTML = '';
        
        this.data[tab].forEach(item => {
            const itemElement = this.createItemElement(tab, item);
            container.appendChild(itemElement);
        });
    }

    createItemElement(type, item) {
        const div = document.createElement('div');
        div.className = 'admin-item';
        div.setAttribute('data-id', item.id);
        
        let content = '';
        
        switch (type) {
            case 'services':
                content = `
                    <h4>${item.title}</h4>
                    <p>${item.description}</p>
                    <div class="service-features">
                        ${item.features.map(feature => `<span class="tag">${feature}</span>`).join('')}
                    </div>
                `;
                break;
            case 'projects':
                content = `
                    <h4>${item.title}</h4>
                    <p>${item.description}</p>
                    <div class="project-tags">
                        ${item.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                    <div class="project-status">Status: <strong>${item.status}</strong></div>
                `;
                break;
            case 'team':
                content = `
                    <h4>${item.name}</h4>
                    <p class="team-role">${item.role}</p>
                    <p>${item.bio}</p>
                `;
                break;
            case 'blog':
                content = `
                    <h4>${item.title}</h4>
                    <p>${item.description}</p>
                    <div class="blog-meta">
                        <span class="blog-category">${item.category}</span>
                        <span class="blog-date">${item.date}</span>
                    </div>
                    <div class="blog-author">By: ${item.author}</div>
                `;
                break;
        }
        
        content += `
            <div class="admin-item-actions">
                <button class="edit-btn" onclick="window.adminManager.editItem('${type}', ${item.id})">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button class="delete-btn" onclick="window.adminManager.deleteItem('${type}', ${item.id})">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        `;
        
        div.innerHTML = content;
        return div;
    }

    openAddModal(type) {
        this.editingItem = null;
        this.openModal(type, null);
    }

    editItem(type, id) {
        const item = this.data[type].find(item => item.id === id);
        if (item) {
            this.editingItem = { type, id };
            this.openModal(type, item);
        }
    }

    deleteItem(type, id) {
        if (confirm('Are you sure you want to delete this item?')) {
            this.data[type] = this.data[type].filter(item => item.id !== id);
            this.loadTabContent(type);
            this.updateStats();
            this.updatePublicPages();
            this.showSuccessMessage('Item deleted successfully');
        }
    }

    openModal(type, item) {
        const modal = document.getElementById('edit-modal');
        const modalTitle = document.getElementById('modal-title');
        const modalFields = document.getElementById('modal-fields');
        
        modalTitle.textContent = item ? `Edit ${type.slice(0, -1)}` : `Add New ${type.slice(0, -1)}`;
        
        // Generate form fields based on type
        modalFields.innerHTML = this.generateFormFields(type, item);
        
        modal.classList.add('active');
    }

    generateFormFields(type, item) {
        let fields = '';
        
        switch (type) {
            case 'services':
                fields = `
                    <div class="form-group">
                        <label for="title">Service Title</label>
                        <input type="text" id="title" name="title" value="${item?.title || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" required>${item?.description || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label for="icon">Icon Class</label>
                        <input type="text" id="icon" name="icon" value="${item?.icon || 'fas fa-shield-alt'}" required>
                    </div>
                    <div class="form-group">
                        <label for="features">Features (comma-separated)</label>
                        <textarea id="features" name="features" required>${item?.features?.join(', ') || ''}</textarea>
                    </div>
                `;
                break;
            case 'projects':
                fields = `
                    <div class="form-group">
                        <label for="title">Project Title</label>
                        <input type="text" id="title" name="title" value="${item?.title || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" required>${item?.description || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label for="tags">Tags (comma-separated)</label>
                        <input type="text" id="tags" name="tags" value="${item?.tags?.join(', ') || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="status" name="status" required>
                            <option value="In Progress" ${item?.status === 'In Progress' ? 'selected' : ''}>In Progress</option>
                            <option value="Completed" ${item?.status === 'Completed' ? 'selected' : ''}>Completed</option>
                            <option value="On Hold" ${item?.status === 'On Hold' ? 'selected' : ''}>On Hold</option>
                        </select>
                    </div>
                `;
                break;
            case 'team':
                fields = `
                    <div class="form-group">
                        <label for="name">Name</label>
                        <input type="text" id="name" name="name" value="${item?.name || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="role">Role</label>
                        <input type="text" id="role" name="role" value="${item?.role || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="bio">Bio</label>
                        <textarea id="bio" name="bio" required>${item?.bio || ''}</textarea>
                    </div>
                `;
                break;
            case 'blog':
                fields = `
                    <div class="form-group">
                        <label for="title">Post Title</label>
                        <input type="text" id="title" name="title" value="${item?.title || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" required>${item?.description || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label for="category">Category</label>
                        <input type="text" id="category" name="category" value="${item?.category || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="author">Author</label>
                        <input type="text" id="author" name="author" value="${item?.author || this.currentUser}" required>
                    </div>
                `;
                break;
        }
        
        return fields;
    }

    closeModal() {
        const modal = document.getElementById('edit-modal');
        modal.classList.remove('active');
        this.editingItem = null;
    }

    handleSave() {
        const formData = new FormData(document.getElementById('modal-form'));
        const data = Object.fromEntries(formData);
        
        if (this.editingItem) {
            // Edit existing item
            const { type, id } = this.editingItem;
            const itemIndex = this.data[type].findIndex(item => item.id === id);
            
            if (itemIndex !== -1) {
                this.data[type][itemIndex] = { ...this.data[type][itemIndex], ...this.processFormData(type, data) };
                this.showSuccessMessage('Item updated successfully');
            }
        } else {
            // Add new item
            const type = this.currentTab;
            const newId = Math.max(...this.data[type].map(item => item.id)) + 1;
            const newItem = { id: newId, ...this.processFormData(type, data) };
            
            this.data[type].push(newItem);
            this.showSuccessMessage('Item added successfully');
        }
        
        this.loadTabContent(this.currentTab);
        this.updateStats();
        this.updatePublicPages();
        this.closeModal();
    }

    processFormData(type, data) {
        const processed = { ...data };
        
        // Process specific fields based on type
        if (type === 'services' && data.features) {
            processed.features = data.features.split(',').map(f => f.trim());
        }
        
        if (type === 'projects' && data.tags) {
            processed.tags = data.tags.split(',').map(t => t.trim());
        }
        
        if (type === 'blog' && !data.date) {
            processed.date = new Date().toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'short', 
                day: 'numeric' 
            });
        }
        
        return processed;
    }

    updatePublicPages() {
        // Update the public pages with new data
        // This would typically involve updating the DOM elements
        // For now, we'll just trigger a page refresh for the affected sections
        
        if (window.navigationManager) {
            // Force refresh of current page content if it's one of the managed pages
            const currentPage = window.navigationManager.currentPage;
            if (['services', 'projects', 'team', 'blog'].includes(currentPage)) {
                window.navigationManager.triggerPageAnimations(currentPage);
            }
        }
    }

    showSuccessMessage(message) {
        if (window.navigationManager) {
            window.navigationManager.showNotification(message, 'success');
        }
    }

    showErrorMessage(message) {
        if (window.navigationManager) {
            window.navigationManager.showNotification(message, 'error');
        }
    }
}

// Initialize admin manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adminManager = new AdminManager();
});
