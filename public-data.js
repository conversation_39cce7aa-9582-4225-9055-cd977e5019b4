// Public Data Renderer - Links admin data to public pages
class PublicDataRenderer {
    constructor() {
        this.init();
    }

    init() {
        // Wait for admin manager to be available
        this.waitForAdminManager();
    }

    waitForAdminManager() {
        if (window.adminManager) {
            this.renderAllPublicData();
        } else {
            setTimeout(() => this.waitForAdminManager(), 100);
        }
    }

    renderAllPublicData() {
        this.renderServices();
        this.renderProjects();
        this.renderTeam();
        this.renderBlog();
    }

    renderServices() {
        const container = document.getElementById('public-services-grid');
        if (!container || !window.adminManager) return;

        const services = window.adminManager.data.services;
        container.innerHTML = '';

        services.forEach(service => {
            const serviceCard = this.createServiceCard(service);
            container.appendChild(serviceCard);
        });
    }

    createServiceCard(service) {
        const card = document.createElement('div');
        card.className = 'service-card';
        
        card.innerHTML = `
            <div class="service-icon"><i class="${service.icon}"></i></div>
            <h3>${service.title}</h3>
            <p>${service.description}</p>
            <ul>
                ${service.features.map(feature => `<li>${feature}</li>`).join('')}
            </ul>
        `;
        
        return card;
    }

    renderProjects() {
        const container = document.getElementById('public-projects-grid');
        if (!container || !window.adminManager) return;

        const projects = window.adminManager.data.projects;
        container.innerHTML = '';

        projects.forEach(project => {
            const projectCard = this.createProjectCard(project);
            container.appendChild(projectCard);
        });
    }

    createProjectCard(project) {
        const card = document.createElement('div');
        card.className = 'project-card';
        
        card.innerHTML = `
            <div class="project-image" style="background-image: url('${project.image}'); background-size: cover; background-position: center;">
                <div class="project-overlay">
                    <a href="${project.link}" target="_blank" class="project-btn">
                        <i class="fas fa-${project.linkType === 'demo' ? 'external-link-alt' : 'download'}"></i>
                        ${project.linkType === 'demo' ? 'View Demo' : 'Download'}
                    </a>
                </div>
            </div>
            <div class="project-content">
                <h3>${project.title}</h3>
                <p>${project.description}</p>
                <div class="project-tags">
                    ${project.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                </div>
                <div class="project-status">
                    <span class="status-badge status-${project.status.toLowerCase().replace(' ', '-')}">
                        ${project.status}
                    </span>
                </div>
            </div>
        `;
        
        return card;
    }

    renderTeam() {
        const container = document.getElementById('public-team-grid');
        if (!container || !window.adminManager) return;

        const team = window.adminManager.data.team;
        container.innerHTML = '';

        team.forEach(member => {
            const teamCard = this.createTeamCard(member);
            container.appendChild(teamCard);
        });
    }

    createTeamCard(member) {
        const card = document.createElement('div');
        card.className = 'team-card';
        
        card.innerHTML = `
            <div class="team-avatar">
                <img src="${member.avatar}" alt="${member.name}" class="avatar-image">
            </div>
            <h3>${member.name}</h3>
            <p class="team-role">${member.role}</p>
            <p class="team-bio">${member.bio}</p>
            <div class="team-social">
                ${member.social.linkedin ? `<a href="${member.social.linkedin}" target="_blank" title="LinkedIn"><i class="fab fa-linkedin"></i></a>` : ''}
                ${member.social.twitter ? `<a href="${member.social.twitter}" target="_blank" title="Twitter"><i class="fab fa-twitter"></i></a>` : ''}
                ${member.social.github ? `<a href="${member.social.github}" target="_blank" title="GitHub"><i class="fab fa-github"></i></a>` : ''}
            </div>
        `;
        
        return card;
    }

    renderBlog() {
        const container = document.getElementById('public-blog-grid');
        if (!container || !window.adminManager) return;

        const blog = window.adminManager.data.blog;
        container.innerHTML = '';

        blog.forEach(post => {
            const blogCard = this.createBlogCard(post);
            container.appendChild(blogCard);
        });
    }

    createBlogCard(post) {
        const card = document.createElement('article');
        card.className = 'blog-card';
        
        card.innerHTML = `
            <div class="blog-image" style="background-image: url('${post.image}'); background-size: cover; background-position: center; height: 200px; border-radius: 10px; margin-bottom: 20px;">
            </div>
            <div class="blog-meta">
                <span class="blog-date">${post.date}</span>
                <span class="blog-category">${post.category}</span>
            </div>
            <h3>${post.title}</h3>
            <p>${post.description}</p>
            <div class="blog-author">By: ${post.author}</div>
            <a href="${post.link}" target="_blank" class="blog-link">
                Read More <i class="fas fa-arrow-right"></i>
            </a>
        `;
        
        return card;
    }

    // Method to update public pages when admin data changes
    updatePublicData() {
        this.renderAllPublicData();
        
        // Trigger page animations for currently visible page
        if (window.navigationManager) {
            const currentPage = window.navigationManager.currentPage;
            if (['services', 'projects', 'team', 'blog'].includes(currentPage)) {
                setTimeout(() => {
                    window.navigationManager.triggerPageAnimations(currentPage);
                }, 100);
            }
        }
    }
}

// Initialize public data renderer
document.addEventListener('DOMContentLoaded', () => {
    window.publicDataRenderer = new PublicDataRenderer();
});
