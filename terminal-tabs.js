// Terminal Tabs System
class TerminalTabs {
    constructor() {
        this.tabs = [];
        this.activeTabId = null;
        this.nextTabId = 1;
        this.container = null;
        this.tabBar = null;
        this.tabContent = null;
        this.contextMenu = null;
        
        this.tabTypes = {
            main: { name: 'Main', icon: '🏠', color: '#00ff41' },
            security: { name: 'Security', icon: '🔒', color: '#ff0080' },
            tools: { name: 'Tools', icon: '🔧', color: '#ffaa00' },
            monitor: { name: 'Monitor', icon: '📊', color: '#00ffff' },
            logs: { name: 'Logs', icon: '📋', color: '#ff6600' },
            custom: { name: 'Custom', icon: '⚡', color: '#ff3333' }
        };
        
        this.init();
    }
    
    init() {
        this.createTabSystem();
        this.setupEventListeners();
        this.createDefaultTab();
        this.loadSavedTabs();
    }
    
    createTabSystem() {
        const homeTerminal = document.querySelector('.home-terminal');
        if (!homeTerminal) return;
        
        // Create tabs container
        this.container = document.createElement('div');
        this.container.className = 'terminal-tabs-container';
        
        // Create tab bar
        this.tabBar = document.createElement('div');
        this.tabBar.className = 'terminal-tab-bar';
        
        // Create new tab button
        const newTabBtn = document.createElement('button');
        newTabBtn.className = 'new-tab-btn';
        newTabBtn.innerHTML = '+';
        newTabBtn.title = 'New Tab (Ctrl+T)';
        this.tabBar.appendChild(newTabBtn);
        
        // Create tab content area
        this.tabContent = document.createElement('div');
        this.tabContent.className = 'terminal-tabs-content';
        
        // Create shortcuts display
        const shortcuts = document.createElement('div');
        shortcuts.className = 'tab-shortcuts';
        shortcuts.innerHTML = `
            Ctrl+T: New Tab | Ctrl+W: Close Tab | Ctrl+Tab: Next Tab
        `;
        
        // Assemble the structure
        this.container.appendChild(this.tabBar);
        this.container.appendChild(this.tabContent);
        this.container.appendChild(shortcuts);
        
        // Replace the existing terminal body
        const terminalBody = homeTerminal.querySelector('.terminal-body');
        if (terminalBody) {
            homeTerminal.replaceChild(this.container, terminalBody);
        }
        
        // Create context menu
        this.createContextMenu();
    }
    
    createContextMenu() {
        this.contextMenu = document.createElement('div');
        this.contextMenu.className = 'tab-context-menu';
        this.contextMenu.style.display = 'none';
        this.contextMenu.innerHTML = `
            <div class="tab-context-item" data-action="rename">Rename Tab</div>
            <div class="tab-context-item" data-action="duplicate">Duplicate Tab</div>
            <div class="tab-context-separator"></div>
            <div class="tab-context-item" data-action="close">Close Tab</div>
            <div class="tab-context-item" data-action="close-others">Close Other Tabs</div>
            <div class="tab-context-item" data-action="close-right">Close Tabs to Right</div>
            <div class="tab-context-separator"></div>
            <div class="tab-context-item" data-action="move-left">Move Left</div>
            <div class="tab-context-item" data-action="move-right">Move Right</div>
        `;
        
        document.body.appendChild(this.contextMenu);
    }
    
    setupEventListeners() {
        // New tab button
        this.tabBar.querySelector('.new-tab-btn').addEventListener('click', () => {
            this.createTab('Custom Terminal', 'custom');
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey) {
                switch(e.key) {
                    case 't':
                        e.preventDefault();
                        this.createTab('Custom Terminal', 'custom');
                        break;
                    case 'w':
                        e.preventDefault();
                        this.closeActiveTab();
                        break;
                    case 'Tab':
                        e.preventDefault();
                        this.switchToNextTab();
                        break;
                }
            }
            
            // Number keys for tab switching
            if (e.ctrlKey && e.key >= '1' && e.key <= '9') {
                e.preventDefault();
                const tabIndex = parseInt(e.key) - 1;
                if (this.tabs[tabIndex]) {
                    this.switchToTab(this.tabs[tabIndex].id);
                }
            }
        });
        
        // Context menu
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.tab-context-menu')) {
                this.hideContextMenu();
            }
        });
        
        this.contextMenu.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            if (action) {
                this.handleContextAction(action);
                this.hideContextMenu();
            }
        });
    }
    
    createTab(title, type = 'custom', content = null) {
        const tabId = this.nextTabId++;
        
        // Create tab element
        const tabElement = document.createElement('div');
        tabElement.className = `terminal-tab type-${type}`;
        tabElement.dataset.tabId = tabId;
        
        tabElement.innerHTML = `
            <div class="tab-icon"></div>
            <div class="tab-title">${title}</div>
            <button class="tab-close">×</button>
            <div class="tab-status"></div>
        `;
        
        // Create tab content
        const tabPane = document.createElement('div');
        tabPane.className = 'terminal-tab-pane';
        tabPane.dataset.tabId = tabId;
        
        if (content) {
            tabPane.innerHTML = content;
        } else {
            tabPane.innerHTML = this.createDefaultTabContent(type);
        }
        
        // Add to arrays
        const tab = {
            id: tabId,
            title: title,
            type: type,
            element: tabElement,
            pane: tabPane,
            isActive: false,
            hasActivity: false,
            isLoading: false
        };
        
        this.tabs.push(tab);
        
        // Insert tab before new tab button
        const newTabBtn = this.tabBar.querySelector('.new-tab-btn');
        this.tabBar.insertBefore(tabElement, newTabBtn);
        this.tabContent.appendChild(tabPane);
        
        // Setup tab events
        this.setupTabEvents(tab);
        
        // Switch to new tab
        this.switchToTab(tabId);
        
        // Animation
        tabElement.classList.add('new-tab');
        setTimeout(() => tabElement.classList.remove('new-tab'), 300);
        
        return tab;
    }
    
    createDefaultTabContent(type) {
        switch(type) {
            case 'main':
                return this.getMainTerminalContent();
            case 'security':
                return this.getSecurityTerminalContent();
            case 'tools':
                return this.getToolsTerminalContent();
            case 'monitor':
                return this.getMonitorTerminalContent();
            case 'logs':
                return this.getLogsTerminalContent();
            default:
                return this.getCustomTerminalContent();
        }
    }
    
    getMainTerminalContent() {
        return `
            <div class="terminal-body">
                <div class="terminal-content">
                    <div class="ascii-art">
 ██████╗ ██████╗  ██████╗ 
██╔════╝██╔════╝ ██╔════╝ 
██║     ██║  ███╗██║  ███╗
██║     ██║   ██║██║   ██║
╚██████╗╚██████╔╝╚██████╔╝
 ╚═════╝ ╚═════╝  ╚═════╝ 
                    </div>
                    <div class="terminal-welcome-text">Main Terminal - Code Guard Group</div>
                    <div class="terminal-subtitle">Primary system interface</div>
                </div>
                <div class="terminal-input-line">
                    <span class="terminal-prompt">root@cgg:~#</span>
                    <span class="terminal-command"></span>
                    <span class="terminal-cursor">█</span>
                </div>
            </div>
        `;
    }
    
    getSecurityTerminalContent() {
        return `
            <div class="terminal-body">
                <div class="terminal-content">
                    <div class="terminal-welcome-text">🔒 Security Terminal</div>
                    <div class="terminal-subtitle">Advanced security monitoring and tools</div>
                    <div class="terminal-line">
                        <span class="output-success">✅ Firewall Status: Active</span>
                    </div>
                    <div class="terminal-line">
                        <span class="output-info">📊 Threat Level: Low</span>
                    </div>
                    <div class="terminal-line">
                        <span class="output-warning">⚠️ Last Scan: 2 minutes ago</span>
                    </div>
                </div>
                <div class="terminal-input-line">
                    <span class="terminal-prompt">security@cgg:~$</span>
                    <span class="terminal-command"></span>
                    <span class="terminal-cursor">█</span>
                </div>
            </div>
        `;
    }
    
    getToolsTerminalContent() {
        return `
            <div class="terminal-body">
                <div class="terminal-content">
                    <div class="terminal-welcome-text">🔧 Tools Terminal</div>
                    <div class="terminal-subtitle">Development and system tools</div>
                    <div class="terminal-line">
                        <span class="output-info">Available tools:</span>
                    </div>
                    <div class="terminal-line">
                        <span class="output-text">  • Code analyzer</span>
                    </div>
                    <div class="terminal-line">
                        <span class="output-text">  • Performance monitor</span>
                    </div>
                    <div class="terminal-line">
                        <span class="output-text">  • Network scanner</span>
                    </div>
                </div>
                <div class="terminal-input-line">
                    <span class="terminal-prompt">tools@cgg:~$</span>
                    <span class="terminal-command"></span>
                    <span class="terminal-cursor">█</span>
                </div>
            </div>
        `;
    }
    
    getMonitorTerminalContent() {
        return `
            <div class="terminal-body">
                <div class="terminal-content">
                    <div class="terminal-welcome-text">📊 System Monitor</div>
                    <div class="terminal-subtitle">Real-time system monitoring</div>
                    <div class="terminal-line">
                        <span class="output-success">CPU Usage: 23%</span>
                    </div>
                    <div class="terminal-line">
                        <span class="output-info">Memory: 4.2GB / 16GB</span>
                    </div>
                    <div class="terminal-line">
                        <span class="output-warning">Network: 125 MB/s</span>
                    </div>
                </div>
                <div class="terminal-input-line">
                    <span class="terminal-prompt">monitor@cgg:~$</span>
                    <span class="terminal-command"></span>
                    <span class="terminal-cursor">█</span>
                </div>
            </div>
        `;
    }
    
    getLogsTerminalContent() {
        return `
            <div class="terminal-body">
                <div class="terminal-content">
                    <div class="terminal-welcome-text">📋 System Logs</div>
                    <div class="terminal-subtitle">System and security logs</div>
                    <div class="terminal-line">
                        <span class="output-timestamp">[2024-01-15 10:30:15]</span>
                        <span class="output-info">System startup completed</span>
                    </div>
                    <div class="terminal-line">
                        <span class="output-timestamp">[2024-01-15 10:30:20]</span>
                        <span class="output-success">Security scan initiated</span>
                    </div>
                </div>
                <div class="terminal-input-line">
                    <span class="terminal-prompt">logs@cgg:~$</span>
                    <span class="terminal-command"></span>
                    <span class="terminal-cursor">█</span>
                </div>
            </div>
        `;
    }
    
    getCustomTerminalContent() {
        return `
            <div class="terminal-body">
                <div class="terminal-content">
                    <div class="terminal-welcome-text">⚡ Custom Terminal</div>
                    <div class="terminal-subtitle">Customizable terminal session</div>
                    <div class="terminal-line">
                        <span class="output-info">Ready for custom commands...</span>
                    </div>
                </div>
                <div class="terminal-input-line">
                    <span class="terminal-prompt">user@cgg:~$</span>
                    <span class="terminal-command"></span>
                    <span class="terminal-cursor">█</span>
                </div>
            </div>
        `;
    }
    
    setupTabEvents(tab) {
        // Tab click
        tab.element.addEventListener('click', (e) => {
            if (!e.target.classList.contains('tab-close')) {
                this.switchToTab(tab.id);
            }
        });
        
        // Tab close
        tab.element.querySelector('.tab-close').addEventListener('click', (e) => {
            e.stopPropagation();
            this.closeTab(tab.id);
        });
        
        // Tab context menu
        tab.element.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showContextMenu(e.clientX, e.clientY, tab.id);
        });
        
        // Tab drag (basic implementation)
        tab.element.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', tab.id);
            tab.element.classList.add('dragging');
        });
        
        tab.element.addEventListener('dragend', () => {
            tab.element.classList.remove('dragging');
        });
    }
    
    switchToTab(tabId) {
        // Deactivate current tab
        if (this.activeTabId) {
            const currentTab = this.getTab(this.activeTabId);
            if (currentTab) {
                currentTab.element.classList.remove('active');
                currentTab.pane.classList.remove('active');
                currentTab.isActive = false;
            }
        }
        
        // Activate new tab
        const newTab = this.getTab(tabId);
        if (newTab) {
            newTab.element.classList.add('active');
            newTab.pane.classList.add('active');
            newTab.isActive = true;
            this.activeTabId = tabId;
            
            // Clear activity indicator
            newTab.hasActivity = false;
            newTab.element.classList.remove('has-activity');
        }
    }
    
    closeTab(tabId) {
        const tab = this.getTab(tabId);
        if (!tab) return;
        
        // Don't close if it's the last tab
        if (this.tabs.length === 1) {
            this.createTab('New Terminal', 'custom');
        }
        
        // Remove from DOM
        tab.element.remove();
        tab.pane.remove();
        
        // Remove from array
        this.tabs = this.tabs.filter(t => t.id !== tabId);
        
        // Switch to another tab if this was active
        if (this.activeTabId === tabId) {
            const nextTab = this.tabs[0];
            if (nextTab) {
                this.switchToTab(nextTab.id);
            }
        }
    }
    
    closeActiveTab() {
        if (this.activeTabId) {
            this.closeTab(this.activeTabId);
        }
    }
    
    switchToNextTab() {
        if (this.tabs.length <= 1) return;
        
        const currentIndex = this.tabs.findIndex(tab => tab.id === this.activeTabId);
        const nextIndex = (currentIndex + 1) % this.tabs.length;
        this.switchToTab(this.tabs[nextIndex].id);
    }
    
    getTab(tabId) {
        return this.tabs.find(tab => tab.id === tabId);
    }
    
    createDefaultTab() {
        this.createTab('Main Terminal', 'main');
    }
    
    showContextMenu(x, y, tabId) {
        this.contextMenu.dataset.tabId = tabId;
        this.contextMenu.style.left = x + 'px';
        this.contextMenu.style.top = y + 'px';
        this.contextMenu.style.display = 'block';
    }
    
    hideContextMenu() {
        this.contextMenu.style.display = 'none';
    }
    
    handleContextAction(action) {
        const tabId = parseInt(this.contextMenu.dataset.tabId);
        const tab = this.getTab(tabId);
        if (!tab) return;
        
        switch(action) {
            case 'rename':
                this.renameTab(tabId);
                break;
            case 'duplicate':
                this.duplicateTab(tabId);
                break;
            case 'close':
                this.closeTab(tabId);
                break;
            case 'close-others':
                this.closeOtherTabs(tabId);
                break;
            case 'close-right':
                this.closeTabsToRight(tabId);
                break;
        }
    }
    
    renameTab(tabId) {
        const tab = this.getTab(tabId);
        if (!tab) return;
        
        const newTitle = prompt('Enter new tab name:', tab.title);
        if (newTitle && newTitle.trim()) {
            tab.title = newTitle.trim();
            tab.element.querySelector('.tab-title').textContent = tab.title;
        }
    }
    
    duplicateTab(tabId) {
        const tab = this.getTab(tabId);
        if (!tab) return;
        
        this.createTab(`${tab.title} (Copy)`, tab.type, tab.pane.innerHTML);
    }
    
    closeOtherTabs(keepTabId) {
        const tabsToClose = this.tabs.filter(tab => tab.id !== keepTabId);
        tabsToClose.forEach(tab => this.closeTab(tab.id));
    }
    
    closeTabsToRight(tabId) {
        const tabIndex = this.tabs.findIndex(tab => tab.id === tabId);
        const tabsToClose = this.tabs.slice(tabIndex + 1);
        tabsToClose.forEach(tab => this.closeTab(tab.id));
    }
    
    loadSavedTabs() {
        // This would load saved tabs from localStorage
        // For now, create some default tabs
        setTimeout(() => {
            this.createTab('Security Monitor', 'security');
            this.createTab('System Tools', 'tools');
        }, 500);
    }
    
    // Public API methods
    addTab(title, type, content) {
        return this.createTab(title, type, content);
    }
    
    setTabActivity(tabId, hasActivity = true) {
        const tab = this.getTab(tabId);
        if (tab && !tab.isActive) {
            tab.hasActivity = hasActivity;
            tab.element.classList.toggle('has-activity', hasActivity);
        }
    }
    
    setTabLoading(tabId, isLoading = true) {
        const tab = this.getTab(tabId);
        if (tab) {
            tab.isLoading = isLoading;
            tab.element.classList.toggle('is-loading', isLoading);
        }
    }
}

// Initialize terminal tabs
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.home-terminal')) {
        window.terminalTabs = new TerminalTabs();
    }
});
