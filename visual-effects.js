// Visual Effects Manager
class VisualEffects {
    constructor() {
        this.settings = {
            glitchEnabled: true,
            neonEnabled: true,
            glitchIntensity: 'medium', // low, medium, high
            neonIntensity: 'medium',
            autoEffects: true,
            effectsFrequency: 5000 // milliseconds
        };
        
        this.glitchElements = [];
        this.neonElements = [];
        this.effectsInterval = null;
        
        this.init();
    }
    
    init() {
        this.loadSettings();
        this.setupElements();
        this.startAutoEffects();
        this.setupEventListeners();
    }
    
    loadSettings() {
        const saved = localStorage.getItem('visualEffectsSettings');
        if (saved) {
            try {
                this.settings = { ...this.settings, ...JSON.parse(saved) };
            } catch (e) {
                console.log('Error loading visual effects settings:', e);
            }
        }
    }
    
    saveSettings() {
        localStorage.setItem('visualEffectsSettings', JSON.stringify(this.settings));
    }
    
    setupElements() {
        // Apply effects to terminal elements
        this.applyGlitchToElements();
        this.applyNeonToElements();
    }
    
    applyGlitchToElements() {
        // Add glitch effects to specific elements
        const targets = [
            '.ascii-art',
            '.terminal-welcome-text',
            '.terminal-title',
            '.command-name'
        ];
        
        targets.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                this.addGlitchEffect(element);
            });
        });
    }
    
    applyNeonToElements() {
        // Add neon effects to specific elements
        const targets = [
            '.terminal-prompt',
            '.terminal-cursor',
            '.floating-btn',
            '.theme-btn'
        ];
        
        targets.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                this.addNeonEffect(element);
            });
        });
    }
    
    addGlitchEffect(element, intensity = 'medium') {
        if (!this.settings.glitchEnabled) return;
        
        // Store original text for glitch effect
        const text = element.textContent || element.innerText;
        element.setAttribute('data-text', text);
        
        // Add glitch classes based on intensity
        switch(intensity) {
            case 'low':
                element.classList.add('glitch');
                break;
            case 'medium':
                element.classList.add('glitch', 'terminal-glitch');
                break;
            case 'high':
                element.classList.add('glitch', 'glitch-intense');
                break;
        }
        
        this.glitchElements.push(element);
    }
    
    addNeonEffect(element, intensity = 'medium') {
        if (!this.settings.neonEnabled) return;
        
        // Add neon classes based on intensity
        switch(intensity) {
            case 'low':
                element.classList.add('neon');
                break;
            case 'medium':
                element.classList.add('neon', 'neon-interactive');
                break;
            case 'high':
                element.classList.add('neon-intense');
                break;
        }
        
        this.neonElements.push(element);
    }
    
    triggerRandomGlitch() {
        if (!this.settings.glitchEnabled || this.glitchElements.length === 0) return;
        
        const randomElement = this.glitchElements[Math.floor(Math.random() * this.glitchElements.length)];
        
        // Add temporary intense glitch
        randomElement.classList.add('glitch-active');
        
        setTimeout(() => {
            randomElement.classList.remove('glitch-active');
        }, 500);
    }
    
    triggerGlitchBurst() {
        if (!this.settings.glitchEnabled) return;
        
        // Apply glitch to multiple elements simultaneously
        const elementsToGlitch = this.glitchElements.slice(0, Math.min(5, this.glitchElements.length));
        
        elementsToGlitch.forEach((element, index) => {
            setTimeout(() => {
                element.classList.add('glitch-intense');
                setTimeout(() => {
                    element.classList.remove('glitch-intense');
                }, 300);
            }, index * 100);
        });
    }
    
    triggerNeonPulse() {
        if (!this.settings.neonEnabled || this.neonElements.length === 0) return;
        
        this.neonElements.forEach((element, index) => {
            setTimeout(() => {
                element.classList.add('neon-intense');
                setTimeout(() => {
                    element.classList.remove('neon-intense');
                    element.classList.add('neon');
                }, 1000);
            }, index * 50);
        });
    }
    
    createGlitchText(text, container) {
        const glitchElement = document.createElement('span');
        glitchElement.className = 'glitch glitch-rgb';
        glitchElement.setAttribute('data-text', text);
        glitchElement.textContent = text;
        
        if (container) {
            container.appendChild(glitchElement);
        }
        
        return glitchElement;
    }
    
    createNeonText(text, container) {
        const neonElement = document.createElement('span');
        neonElement.className = 'neon neon-typing';
        neonElement.textContent = text;
        
        if (container) {
            container.appendChild(neonElement);
        }
        
        return neonElement;
    }
    
    startAutoEffects() {
        if (!this.settings.autoEffects) return;
        
        this.effectsInterval = setInterval(() => {
            const randomEffect = Math.random();
            
            if (randomEffect < 0.3) {
                this.triggerRandomGlitch();
            } else if (randomEffect < 0.6) {
                this.triggerNeonPulse();
            } else if (randomEffect < 0.8) {
                this.triggerGlitchBurst();
            }
            
        }, this.settings.effectsFrequency);
    }
    
    stopAutoEffects() {
        if (this.effectsInterval) {
            clearInterval(this.effectsInterval);
            this.effectsInterval = null;
        }
    }
    
    setupEventListeners() {
        // Listen for theme changes
        document.addEventListener('themeChanged', () => {
            this.updateEffectsForTheme();
        });
        
        // Listen for clicks to trigger effects
        document.addEventListener('click', (e) => {
            if (e.target.closest('.terminal-content')) {
                this.triggerRandomGlitch();
            }
        });
        
        // Listen for command execution
        document.addEventListener('commandExecuted', () => {
            this.triggerNeonPulse();
        });
        
        // Listen for typing
        document.addEventListener('keydown', (e) => {
            if (e.key.length === 1) { // Only for character keys
                this.triggerSubtleGlitch();
            }
        });
    }
    
    triggerSubtleGlitch() {
        if (!this.settings.glitchEnabled) return;
        
        const randomElement = this.glitchElements[Math.floor(Math.random() * this.glitchElements.length)];
        if (randomElement) {
            randomElement.style.animation = 'glitch-1 0.1s ease-out';
            setTimeout(() => {
                randomElement.style.animation = '';
            }, 100);
        }
    }
    
    updateEffectsForTheme() {
        // Adjust effect intensities based on current theme
        const currentTheme = window.terminalThemes?.getCurrentTheme() || 'matrix';
        
        switch(currentTheme) {
            case 'cyberpunk':
                this.settings.effectsFrequency = 3000; // More frequent
                this.settings.glitchIntensity = 'high';
                this.settings.neonIntensity = 'high';
                break;
            case 'hacker':
                this.settings.effectsFrequency = 2000; // Very frequent
                this.settings.glitchIntensity = 'high';
                this.settings.neonIntensity = 'medium';
                break;
            case 'retro':
                this.settings.effectsFrequency = 8000; // Less frequent
                this.settings.glitchIntensity = 'low';
                this.settings.neonIntensity = 'medium';
                break;
            default: // matrix
                this.settings.effectsFrequency = 5000; // Default
                this.settings.glitchIntensity = 'medium';
                this.settings.neonIntensity = 'medium';
        }
        
        // Restart auto effects with new frequency
        this.stopAutoEffects();
        this.startAutoEffects();
    }
    
    setGlitchEnabled(enabled) {
        this.settings.glitchEnabled = enabled;
        
        if (!enabled) {
            // Remove glitch effects
            this.glitchElements.forEach(element => {
                element.classList.remove('glitch', 'glitch-intense', 'glitch-rgb', 'terminal-glitch', 'glitch-active');
            });
        } else {
            // Re-apply glitch effects
            this.applyGlitchToElements();
        }
        
        this.saveSettings();
    }
    
    setNeonEnabled(enabled) {
        this.settings.neonEnabled = enabled;
        
        if (!enabled) {
            // Remove neon effects
            this.neonElements.forEach(element => {
                element.classList.remove('neon', 'neon-intense', 'neon-interactive');
            });
        } else {
            // Re-apply neon effects
            this.applyNeonToElements();
        }
        
        this.saveSettings();
    }
    
    setAutoEffects(enabled) {
        this.settings.autoEffects = enabled;
        
        if (enabled) {
            this.startAutoEffects();
        } else {
            this.stopAutoEffects();
        }
        
        this.saveSettings();
    }
    
    // Public methods for manual control
    manualGlitch() {
        this.triggerGlitchBurst();
    }
    
    manualNeonPulse() {
        this.triggerNeonPulse();
    }
    
    reset() {
        this.stopAutoEffects();
        
        // Remove all effects
        this.glitchElements.forEach(element => {
            element.classList.remove('glitch', 'glitch-intense', 'glitch-rgb', 'terminal-glitch', 'glitch-active');
        });
        
        this.neonElements.forEach(element => {
            element.classList.remove('neon', 'neon-intense', 'neon-interactive');
        });
        
        // Clear arrays
        this.glitchElements = [];
        this.neonElements = [];
        
        // Restart
        this.setupElements();
        this.startAutoEffects();
    }
}

// Initialize visual effects
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize on home page
    if (document.querySelector('.home-terminal')) {
        window.visualEffects = new VisualEffects();
    }
});
