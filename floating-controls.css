/* Floating Control Panel - Available on all pages */
.floating-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    z-index: 10000;
    opacity: 0.9;
    transition: all 0.3s ease;
    user-select: none;
    backdrop-filter: blur(10px);
}

.floating-controls.dragging {
    opacity: 1;
    transform: scale(1.05);
}

.floating-controls.hidden {
    opacity: 0;
    pointer-events: none;
    transform: translateY(20px);
}

/* Control Button Styles */
.floating-btn {
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid #00ff41;
    color: #00ff41;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-family: 'Fira Code', monospace;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    min-width: 80px;
    justify-content: center;
}

.floating-btn:hover {
    background: rgba(0, 255, 65, 0.1);
    border-color: #00ff41;
    box-shadow:
        0 0 15px rgba(0, 255, 65, 0.3),
        0 4px 15px rgba(0, 0, 0, 0.4);
    transform: translateY(-2px);
}

.floating-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.floating-btn.active {
    background: rgba(0, 255, 65, 0.2);
    border-color: #00ff41;
    box-shadow:
        0 0 20px rgba(0, 255, 65, 0.4),
        inset 0 0 10px rgba(0, 255, 65, 0.1);
}

/* Button Icons */
.floating-btn .icon {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Specific Button Styles */
.floating-btn.replay-btn .icon::before {
    content: "⟲";
}

.floating-btn.sound-btn .icon::before {
    content: "🔊";
}

.floating-btn.sound-btn.muted .icon::before {
    content: "🔇";
}

.floating-btn.fullscreen-btn .icon::before {
    content: "⛶";
}

.floating-btn.fullscreen-btn.active .icon::before {
    content: "⛶";
}

/* Drag Handle */
.drag-handle {
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 4px;
    background: rgba(0, 255, 65, 0.3);
    border-radius: 2px;
    cursor: move;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.floating-controls:hover .drag-handle {
    opacity: 1;
}

/* Close Button */
.close-controls {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    background: #ff5f57;
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.floating-controls:hover .close-controls {
    opacity: 1;
}

.close-controls:hover {
    background: #ff3b30;
    transform: scale(1.1);
}

/* Minimized State */
.floating-controls.minimized {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid #00ff41;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.floating-controls.minimized .floating-btn {
    display: none;
}

.floating-controls.minimized::before {
    content: "⚙";
    color: #00ff41;
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .floating-controls {
        bottom: 15px;
        right: 15px;
        gap: 8px;
    }

    .floating-btn {
        padding: 6px 10px;
        font-size: 10px;
        min-width: 70px;
    }

    .drag-handle {
        width: 25px;
        height: 3px;
    }
}

/* Animation for show/hide */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 0.9;
        transform: translateY(0);
    }
}

@keyframes slideOutDown {
    from {
        opacity: 0.9;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(20px);
    }
}

.floating-controls.show {
    animation: slideInUp 0.3s ease forwards;
}

.floating-controls.hide {
    animation: slideOutDown 0.3s ease forwards;
}

/* Pulse effect for active states */
@keyframes controlPulse {
    0%, 100% {
        box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
    }
    50% {
        box-shadow: 0 0 25px rgba(0, 255, 65, 0.5);
    }
}

.floating-btn.pulsing {
    animation: controlPulse 2s ease-in-out infinite;
}
